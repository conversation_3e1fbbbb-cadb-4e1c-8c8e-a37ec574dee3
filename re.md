# Raingad-IM 前端安全审计报告

## 项目概述
- **项目名称**: Raingad-IM
- **版本**: 6.0.1
- **技术栈**: Vue 2.6.14 + Element UI + Axios + WebSocket
- **项目类型**: 即时通讯系统前端

## 严重安全漏洞

### 1. 【高危】XSS跨站脚本攻击漏洞

#### 漏洞描述
系统缺乏对用户输入的有效过滤和转义，存在多处XSS攻击风险。

#### 影响范围
- 聊天消息内容
- 用户昵称和个人资料
- 群组名称和公告
- 文件名显示

#### 具体漏洞位置

**1. 聊天消息内容未过滤**
```javascript
// src/components/message/index.vue
// 消息内容直接渲染，未进行HTML转义
handleSend(message, next, file) {
  // 消息内容直接发送，无XSS过滤
  message.content = userInput; // 危险：未过滤用户输入
}
```

**2. 粘贴内容处理存在XSS风险**
```javascript
// src/utils/index.js 第29-56行
export function pastePlainText(e) {
    let innerText = ''
    // 直接插入HTML内容，存在XSS风险
    document.execCommand('insertHtml', false, uniform(innerText))
}
```

#### HTTP请求示例
```http
POST /enterprise/im/sendMessage HTTP/1.1
Host: target.com
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer [token]

content=<script>alert('XSS')</script>&toContactId=123&type=text
```

### 2. 【高危】认证和会话管理漏洞

#### 漏洞描述
系统存在多个认证相关的安全问题，可能导致账户劫持。

#### 具体问题

**1. Token存储不安全**
```javascript
// src/utils/auth.js
// Token存储在localStorage中，容易被XSS攻击窃取
Lockr.set('authToken', token)
Lockr.set('sessionId', sessionId)
```

**2. 演示模式账户信息泄露**
```javascript
// src/views/Login.vue 第103-108行
if(this.globalConfig.demon_mode){
  const random = Math.floor(Math.random() * 19 + 2)
  this.loginForm.account=***********+random; // 暴露账户规律
  this.loginForm.password='123456'; // 硬编码密码
}
```

**3. 记住密码功能不安全**
```javascript
// src/views/Login.vue
if(this.loginForm.rememberMe){
  Lockr.set('LoginAccount',data); // 明文存储密码
}
```

#### HTTP请求示例
```http
POST /common/pub/login HTTP/1.1
Host: target.com
Content-Type: application/x-www-form-urlencoded

account=***********&password=123456
```

### 3. 【中危】文件上传安全漏洞

#### 漏洞描述
文件上传功能缺乏充分的安全验证，存在恶意文件上传风险。

#### 具体问题

**1. 文件类型验证不充分**
```javascript
// src/components/message/setting/index.vue 第328-336行
before (file) {
  const maxSize = file.size / 1024 / 1024 < this.maxSize;
  // 仅检查文件大小，未验证文件类型和内容
  this.isImg(file.name) // 仅基于文件名判断
}
```

**2. 文件大小限制可绕过**
```javascript
// src/components/message/index.vue 第2021-2025行
if (file.size > (this.globalConfig.fileUpload.size * 1024 * 1024)) {
  // 仅在前端检查，后端验证缺失
  return this.$message.error("上传的内容不等大于"+this.globalConfig.fileUpload.size+"MB！");
}
```

#### HTTP请求示例
```http
POST /common/upload/uploadFile HTTP/1.1
Host: target.com
Content-Type: multipart/form-data
Authorization: Bearer [token]

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="malicious.php.jpg"
Content-Type: image/jpeg

<?php system($_GET['cmd']); ?>
------WebKitFormBoundary--
```

### 4. 【中危】WebSocket安全漏洞

#### 漏洞描述
WebSocket连接缺乏充分的安全验证和消息过滤。

#### 具体问题

**1. WebSocket消息未验证**
```javascript
// src/components/message/socket.vue 第55-78行
websocketOnMessage(e) {
  const data = JSON.parse(e.data); // 直接解析，未验证消息格式
  // 未验证消息来源和完整性
  this.$store.commit('catchSocketAction', data);
}
```

**2. 心跳机制可被滥用**
```javascript
// src/components/message/socket.vue 第102-108行
start() {
  this.heartbeatInterval = setInterval(() => {
    this.websocketSend({"type": "ping"}); // 无频率限制
  }, this.timeout)
}
```

#### WebSocket消息示例
```javascript
// 恶意WebSocket消息
{
  "type": "message",
  "content": "<script>alert('XSS')</script>",
  "fromUser": {"user_id": "attacker"}
}
```

### 5. 【中危】权限控制漏洞

#### 漏洞描述
系统权限验证主要依赖前端，存在权限绕过风险。

#### 具体问题

**1. 管理员权限验证不严格**
```javascript
// src/permission.js 第48-57行
if((userInfo && userInfo.role>0) || demon){
  next() // 演示模式可绕过权限检查
}else{
  Message.error('您没有权限访问该页面');
}
```

**2. 角色信息存储在前端**
```javascript
// src/store/index.js
// 用户角色信息存储在前端，可被篡改
SET_USERINFO: (state, userInfo) => {
  Lockr.set('UserInfo', userInfo) // 包含role等敏感信息
}
```

#### HTTP请求示例
```http
GET /manage/index HTTP/1.1
Host: target.com
Authorization: Bearer [modified_token]
sessionId: [session_id]
```

## 中等风险漏洞

### 6. 【中危】信息泄露漏洞

#### 具体问题

**1. 敏感配置信息暴露**
```javascript
// src/views/Login.vue 第23行
// 演示账号信息直接显示在前端
<div class="c-666">演示账号：***********~13800000020，密码:123456</div>
```

**2. API端点信息泄露**
```javascript
// src/utils/request.js 第47-48行
const apiUrl = window.location.protocol+'//'+ (process.env.NODE_ENV === 'production' ? window.location.host + '/' : process.env.VUE_APP_BASE_API);
window.BASE_URL = apiUrl; // 全局暴露API地址
```

### 7. 【中危】输入验证不充分

#### 具体问题

**1. 邮箱和手机号验证可绕过**
```javascript
// src/views/Register.vue 第126-134行
validateContact(rule, value, callback) {
  // 正则表达式验证不够严格
  if (/^1[3456789]\d{9}$/.test(value) || /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value)) {
    callback();
  }
}
```

**2. 中文字符过滤不完整**
```javascript
// src/views/Register.vue 第122-125行
handleInput(value) {
  const filteredValue = value.replace(/[\u4e00-\u9fa5]/g, ''); // 仅过滤基本中文字符
}
```

## 低风险问题

### 8. 【低危】调试信息泄露

```javascript
// package.json 第79-86行
"rules": {
  "no-console": "off", // 生产环境仍保留console输出
  "no-debugger": "off" // 允许debugger语句
}
```

### 9. 【低危】依赖版本过旧

- axios: ^0.21.4 (存在已知安全漏洞)
- vue: ^2.6.14 (版本较旧)

## 修复建议

### 高优先级修复

1. **实施XSS防护**
   - 对所有用户输入进行HTML转义
   - 使用CSP(Content Security Policy)
   - 实施输出编码

2. **加强认证安全**
   - 使用HttpOnly Cookie存储敏感Token
   - 实施Token刷新机制
   - 移除演示模式硬编码凭据

3. **文件上传安全**
   - 实施服务端文件类型验证
   - 添加文件内容检查
   - 限制文件上传路径

### 中优先级修复

4. **WebSocket安全**
   - 添加消息验证和过滤
   - 实施连接频率限制
   - 加强消息完整性检查

5. **权限控制**
   - 移除前端权限验证依赖
   - 实施服务端权限检查
   - 加强角色管理

### 低优先级修复

6. **依赖更新**
   - 升级axios到最新安全版本
   - 更新其他过时依赖

7. **代码清理**
   - 移除生产环境调试代码
   - 清理敏感信息暴露

## 总结

该即时通讯系统存在多个严重的安全漏洞，主要集中在XSS防护、认证管理和文件上传安全方面。建议立即修复高危漏洞，并建立完善的安全开发流程。
