const apiUrl = window.location.protocol+'//'+ (process.env.NODE_ENV === 'production' ? window.location.host + '/' : process.env.VUE_APP_BASE_API);

// type为表情类型.1是emoji,2是自定义表情
export default [
  {
    label: "表情",
    type:1,
    name:'emoji',
    icon:"cuIcon-emoji",
      children: [
        {
          name: "1f600",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f600.png"
        },
        {
          name: "1f62c",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f62c.png"
        },
        {
          name: "1f601",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f601.png"
        },
        {
          name: "1f602",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f602.png"
        },
        {
          name: "1f923",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f923.png"
        },
        {
          name: "1f973",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f973.png"
        },
      {
        name: "1f974",
        title: "emoji",
        src: apiUrl+"/static/img/emoji/twitter/1f974.png"
      },
      {
        name: "1f979",
        title: "emoji",
        src: apiUrl+"/static/img/emoji/twitter/1f979.png"
      },
        {
          name: "1f603",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f603.png"
        },
        {
          name: "1f604",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f604.png"
        },
        {
          name: "1f605",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f605.png"
        },
        {
          name: "1f606",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f606.png"
        },
        {
          name: "1f607",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f607.png"
        },
      {
          name: "1f608",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f608.png"
        },
        {
          name: "1f609",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f609.png"
        },
        {
          name: "1f60a",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60a.png"
        },
        {
          name: "1f642",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f642.png"
        },
        {
          name: "1f643",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f643.png"
        },
        {
          name: "1263a",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/263a.png"
        },
        {
          name: "1f60b",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60b.png"
        },
        {
          name: "1f60c",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60c.png"
        },
        {
          name: "1f60d",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60d.png"
        },
        {
          name: "1f970",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f970.png"
        },
        {
          name: "1f618",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f618.png"
        },
        {
          name: "1f617",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f617.png"
        },
        {
          name: "1f619",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f619.png"
        },
        {
          name: "1f61a",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61a.png"
        },
        {
          name: "1f61c",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61c.png"
        },
        {
          name: "1f92a",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f92a.png"
        },
        {
          name: "1f928",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f928.png"
        },
        {
          name: "1f9d0",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f9d0.png"
        },
        {
          name: "1f61d",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61d.png"
        },
        {
          name: "1f61b",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61b.png"
        },
        {
          name: "1f911",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f911.png"
        },
        {
          name: "1f913",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f913.png"
        },
        {
          name: "1f60e",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60e.png"
        },
        {
          name: "1f929",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f929.png"
        },
        {
          name: "1f921",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f921.png"
        },
        {
          name: "1f920",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f920.png"
        },
        {
          name: "1f917",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f917.png"
        },
        {
          name: "1f60f",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f60f.png"
        },
        {
          name: "1f636",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f636.png"
        },
        {
          name: "1f610",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f610.png"
        },
        {
          name: "1f611",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f611.png"
        },
        {
          name: "1f612",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f612.png"
        },
        {
          name: "1f644",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f644.png"
        },
        {
          name: "1f914",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f914.png"
        },
        {
          name: "1f925",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f925.png"
        },
      {
        name: "1f927",
        title: "emoji",
        src: apiUrl+"/static/img/emoji/twitter/1f927.png"
      },
      {
        name: "1f928",
        title: "emoji",
        src: apiUrl+"/static/img/emoji/twitter/1f928.png"
      },
      {
        name: "1f929",
        title: "emoji",
        src: apiUrl+"/static/img/emoji/twitter/1f929.png"
      },
        {
          name: "1f92d",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f92d.png"
        },
        {
          name: "1f92b",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f92b.png"
        },
        {
          name: "1f92c",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f92c.png"
        },
        {
          name: "1f92f",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f92f.png"
        },
        {
          name: "1f633",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f633.png"
        },
        {
          name: "1f61e",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61e.png"
        },
        {
          name: "1f61f",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f61f.png"
        },
        {
          name: "1f620",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f620.png"
        },
        {
          name: "1f621",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1f621.png"
        },
        {
          name: "1fae0",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae0.png"
        },
        {
          name: "1fae1",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae1.png"
        },
        {
          name: "1fae2",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae2.png"
        },
        {
          name: "1fae3",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae3.png"
        },
        {
          name: "1fae4",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae4.png"
        },
        {
          name: "1fae5",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1fae5.png"
        },
        {
          name: "1faf0",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/1faf0.png"
        },
        {
          name: "2639",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/2639.png"
        },
        {
          name: "263a",
          title: "emoji",
          src: apiUrl+"/static/img/emoji/twitter/263a.png"
        }
      ]
  },
  {
    label: "收藏",
    type:2,
    icon: "cuIcon-like",
    children: [
      //必须放到这个位置，该列表通过后端获取
    ]
  }
];
