(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e():"function"===typeof define&&define.amd?define([],e):"object"===typeof exports?exports["index"]=e():t["index"]=e()})("undefined"!==typeof self?self:this,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"01f9":function(t,e,n){"use strict";var i=n("2d00"),r=n("5ca1"),o=n("2aba"),a=n("32e9"),s=n("84f2"),c=n("41a0"),l=n("7f20"),h=n("38fd"),d=n("2b4c")("iterator"),u=!([].keys&&"next"in[].keys()),p="@@iterator",m="keys",g="values",f=function(){return this};t.exports=function(t,e,n,v,E,y,b){c(n,e,v);var x,C,T,D=function(t){if(!u&&t in A)return A[t];switch(t){case m:return function(){return new n(this,t)};case g:return function(){return new n(this,t)}}return function(){return new n(this,t)}},w=e+" Iterator",I=E==g,k=!1,A=t.prototype,S=A[d]||A[p]||E&&A[E],L=S||D(E),O=E?I?D("entries"):L:void 0,_="Array"==e&&A.entries||S;if(_&&(T=h(_.call(new t)),T!==Object.prototype&&T.next&&(l(T,w,!0),i||"function"==typeof T[d]||a(T,d,f))),I&&S&&S.name!==g&&(k=!0,L=function(){return S.call(this)}),i&&!b||!u&&!k&&A[d]||a(A,d,L),s[e]=L,s[w]=f,E)if(x={values:I?L:D(g),keys:y?L:D(m),entries:O},b)for(C in x)C in A||o(A,C,x[C]);else r(r.P+r.F*(u||k),e,x);return x}},"02f4":function(t,e,n){var i=n("4588"),r=n("be13");t.exports=function(t){return function(e,n){var o,a,s=String(r(e)),c=i(n),l=s.length;return c<0||c>=l?t?"":void 0:(o=s.charCodeAt(c),o<55296||o>56319||c+1===l||(a=s.charCodeAt(c+1))<56320||a>57343?t?s.charAt(c):o:t?s.slice(c,c+2):a-56320+(o-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var i=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?i(t,e).length:1)}},"0a49":function(t,e,n){var i=n("9b43"),r=n("626a"),o=n("4bf8"),a=n("9def"),s=n("cd1c");t.exports=function(t,e){var n=1==t,c=2==t,l=3==t,h=4==t,d=6==t,u=5==t||d,p=e||s;return function(e,s,m){for(var g,f,v=o(e),E=r(v),y=i(s,m,3),b=a(E.length),x=0,C=n?p(e,b):c?p(e,0):void 0;b>x;x++)if((u||x in E)&&(g=E[x],f=y(g,x,v),t))if(n)C[x]=f;else if(f)switch(t){case 3:return!0;case 5:return g;case 6:return x;case 2:C.push(g)}else if(h)return!1;return d?-1:l||h?h:C}}},"0bfb":function(t,e,n){"use strict";var i=n("cb7c");t.exports=function(){var t=i(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"0d58":function(t,e,n){var i=n("ce10"),r=n("e11e");t.exports=Object.keys||function(t){return i(t,r)}},1169:function(t,e,n){var i=n("2d95");t.exports=Array.isArray||function(t){return"Array"==i(t)}},"117e":function(t,e,n){},"11e9":function(t,e,n){var i=n("52a7"),r=n("4630"),o=n("6821"),a=n("6a99"),s=n("69a8"),c=n("c69a"),l=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?l:function(t,e){if(t=o(t),e=a(e,!0),c)try{return l(t,e)}catch(n){}if(s(t,e))return r(!i.f.call(t,e),t[e])}},1495:function(t,e,n){var i=n("86cc"),r=n("cb7c"),o=n("0d58");t.exports=n("9e1e")?Object.defineProperties:function(t,e){r(t);var n,a=o(e),s=a.length,c=0;while(s>c)i.f(t,n=a[c++],e[n]);return t}},"1c4c":function(t,e,n){"use strict";var i=n("9b43"),r=n("5ca1"),o=n("4bf8"),a=n("1fa8"),s=n("33a4"),c=n("9def"),l=n("f1ae"),h=n("27ee");r(r.S+r.F*!n("5cc5")((function(t){Array.from(t)})),"Array",{from:function(t){var e,n,r,d,u=o(t),p="function"==typeof this?this:Array,m=arguments.length,g=m>1?arguments[1]:void 0,f=void 0!==g,v=0,E=h(u);if(f&&(g=i(g,m>2?arguments[2]:void 0,2)),void 0==E||p==Array&&s(E))for(e=c(u.length),n=new p(e);e>v;v++)l(n,v,f?g(u[v],v):u[v]);else for(d=E.call(u),n=new p;!(r=d.next()).done;v++)l(n,v,f?a(d,g,[r.value,v],!0):r.value);return n.length=v,n}})},"1fa8":function(t,e,n){var i=n("cb7c");t.exports=function(t,e,n,r){try{return r?e(i(n)[0],n[1]):e(n)}catch(a){var o=t["return"];throw void 0!==o&&i(o.call(t)),a}}},"20d6":function(t,e,n){"use strict";var i=n("5ca1"),r=n("0a49")(6),o="findIndex",a=!0;o in[]&&Array(1)[o]((function(){a=!1})),i(i.P+i.F*a,"Array",{findIndex:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")(o)},"20e3":function(t,e,n){"use strict";n("ef69")},"214f":function(t,e,n){"use strict";n("b0c5");var i=n("2aba"),r=n("32e9"),o=n("79e5"),a=n("be13"),s=n("2b4c"),c=n("520a"),l=s("species"),h=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),d=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var u=s(t),p=!o((function(){var e={};return e[u]=function(){return 7},7!=""[t](e)})),m=p?!o((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[l]=function(){return n}),n[u](""),!e})):void 0;if(!p||!m||"replace"===t&&!h||"split"===t&&!d){var g=/./[u],f=n(a,u,""[t],(function(t,e,n,i,r){return e.exec===c?p&&!r?{done:!0,value:g.call(e,n,i)}:{done:!0,value:t.call(n,e,i)}:{done:!1}})),v=f[0],E=f[1];i(String.prototype,t,v),r(RegExp.prototype,u,2==e?function(t,e){return E.call(t,this,e)}:function(t){return E.call(t,this)})}}},"224d":function(t,e,n){},"230e":function(t,e,n){var i=n("d3f4"),r=n("7726").document,o=i(r)&&i(r.createElement);t.exports=function(t){return o?r.createElement(t):{}}},"23c6":function(t,e,n){var i=n("2d95"),r=n("2b4c")("toStringTag"),o="Arguments"==i(function(){return arguments}()),a=function(t,e){try{return t[e]}catch(n){}};t.exports=function(t){var e,n,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=a(e=Object(t),r))?n:o?i(e):"Object"==(s=i(e))&&"function"==typeof e.callee?"Arguments":s}},"260f":function(t,e,n){"use strict";n("43e9")},2621:function(t,e){e.f=Object.getOwnPropertySymbols},2638:function(t,e,n){"use strict";function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e,n=1;n<arguments.length;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},i.apply(this,arguments)}var r=["attrs","props","domProps"],o=["class","style","directives"],a=["on","nativeOn"],s=function(t){return t.reduce((function(t,e){for(var n in e)if(t[n])if(-1!==r.indexOf(n))t[n]=i({},t[n],e[n]);else if(-1!==o.indexOf(n)){var s=t[n]instanceof Array?t[n]:[t[n]],l=e[n]instanceof Array?e[n]:[e[n]];t[n]=[].concat(s,l)}else if(-1!==a.indexOf(n))for(var h in e[n])if(t[n][h]){var d=t[n][h]instanceof Array?t[n][h]:[t[n][h]],u=e[n][h]instanceof Array?e[n][h]:[e[n][h]];t[n][h]=[].concat(d,u)}else t[n][h]=e[n][h];else if("hook"===n)for(var p in e[n])t[n][p]=t[n][p]?c(t[n][p],e[n][p]):e[n][p];else t[n]=e[n];else t[n]=e[n];return t}),{})},c=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=s},"26fe":function(t,e,n){"use strict";n("224d")},"27ee":function(t,e,n){var i=n("23c6"),r=n("2b4c")("iterator"),o=n("84f2");t.exports=n("8378").getIteratorMethod=function(t){if(void 0!=t)return t[r]||t["@@iterator"]||o[i(t)]}},"2aba":function(t,e,n){var i=n("7726"),r=n("32e9"),o=n("69a8"),a=n("ca5a")("src"),s=n("fa5b"),c="toString",l=(""+s).split(c);n("8378").inspectSource=function(t){return s.call(t)},(t.exports=function(t,e,n,s){var c="function"==typeof n;c&&(o(n,"name")||r(n,"name",e)),t[e]!==n&&(c&&(o(n,a)||r(n,a,t[e]?""+t[e]:l.join(String(e)))),t===i?t[e]=n:s?t[e]?t[e]=n:r(t,e,n):(delete t[e],r(t,e,n)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[a]||s.call(this)}))},"2aeb":function(t,e,n){var i=n("cb7c"),r=n("1495"),o=n("e11e"),a=n("613b")("IE_PROTO"),s=function(){},c="prototype",l=function(){var t,e=n("230e")("iframe"),i=o.length,r="<",a=">";e.style.display="none",n("fab2").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(r+"script"+a+"document.F=Object"+r+"/script"+a),t.close(),l=t.F;while(i--)delete l[c][o[i]];return l()};t.exports=Object.create||function(t,e){var n;return null!==t?(s[c]=i(t),n=new s,s[c]=null,n[a]=t):n=l(),void 0===e?n:r(n,e)}},"2b4c":function(t,e,n){var i=n("5537")("wks"),r=n("ca5a"),o=n("7726").Symbol,a="function"==typeof o,s=t.exports=function(t){return i[t]||(i[t]=a&&o[t]||(a?o:r)("Symbol."+t))};s.store=i},"2d00":function(t,e){t.exports=!1},"2d95":function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},"2f21":function(t,e,n){"use strict";var i=n("79e5");t.exports=function(t,e){return!!t&&i((function(){e?t.call(null,(function(){}),1):t.call(null)}))}},"2fdb":function(t,e,n){"use strict";var i=n("5ca1"),r=n("d2c8"),o="includes";i(i.P+i.F*n("5147")(o),"String",{includes:function(t){return!!~r(this,t,o).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"323d":function(t,e,n){},"32e9":function(t,e,n){var i=n("86cc"),r=n("4630");t.exports=n("9e1e")?function(t,e,n){return i.f(t,e,r(1,n))}:function(t,e,n){return t[e]=n,t}},"33a4":function(t,e,n){var i=n("84f2"),r=n("2b4c")("iterator"),o=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||o[r]===t)}},"35f3":function(t,e,n){"use strict";n("dc6d")},3738:function(t,e,n){},3846:function(t,e,n){n("9e1e")&&"g"!=/./g.flags&&n("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:n("0bfb")})},"38fd":function(t,e,n){var i=n("69a8"),r=n("4bf8"),o=n("613b")("IE_PROTO"),a=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=r(t),i(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},"3b2b":function(t,e,n){var i=n("7726"),r=n("5dbc"),o=n("86cc").f,a=n("9093").f,s=n("aae3"),c=n("0bfb"),l=i.RegExp,h=l,d=l.prototype,u=/a/g,p=/a/g,m=new l(u)!==u;if(n("9e1e")&&(!m||n("79e5")((function(){return p[n("2b4c")("match")]=!1,l(u)!=u||l(p)==p||"/a/i"!=l(u,"i")})))){l=function(t,e){var n=this instanceof l,i=s(t),o=void 0===e;return!n&&i&&t.constructor===l&&o?t:r(m?new h(i&&!o?t.source:t,e):h((i=t instanceof l)?t.source:t,i&&o?c.call(t):e),n?this:d,l)};for(var g=function(t){t in l||o(l,t,{configurable:!0,get:function(){return h[t]},set:function(e){h[t]=e}})},f=a(h),v=0;f.length>v;)g(f[v++]);d.constructor=l,l.prototype=d,n("2aba")(i,"RegExp",l)}n("7a56")("RegExp")},"3d91":function(t,e,n){"use strict";n("622b")},"3dfc":function(t,e,n){"use strict";n("3738")},"41a0":function(t,e,n){"use strict";var i=n("2aeb"),r=n("4630"),o=n("7f20"),a={};n("32e9")(a,n("2b4c")("iterator"),(function(){return this})),t.exports=function(t,e,n){t.prototype=i(a,{next:r(1,n)}),o(t,e+" Iterator")}},"43e9":function(t,e,n){},"456d":function(t,e,n){var i=n("4bf8"),r=n("0d58");n("5eda")("keys",(function(){return function(t){return r(i(t))}}))},4588:function(t,e){var n=Math.ceil,i=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?i:n)(t)}},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"4bf8":function(t,e,n){var i=n("be13");t.exports=function(t){return Object(i(t))}},"504c":function(t,e,n){var i=n("9e1e"),r=n("0d58"),o=n("6821"),a=n("52a7").f;t.exports=function(t){return function(e){var n,s=o(e),c=r(s),l=c.length,h=0,d=[];while(l>h)n=c[h++],i&&!a.call(s,n)||d.push(t?[n,s[n]]:s[n]);return d}}},5147:function(t,e,n){var i=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[i]=!1,!"/./"[t](e)}catch(r){}}return!0}},"520a":function(t,e,n){"use strict";var i=n("0bfb"),r=RegExp.prototype.exec,o=String.prototype.replace,a=r,s="lastIndex",c=function(){var t=/a/,e=/b*/g;return r.call(t,"a"),r.call(e,"a"),0!==t[s]||0!==e[s]}(),l=void 0!==/()??/.exec("")[1],h=c||l;h&&(a=function(t){var e,n,a,h,d=this;return l&&(n=new RegExp("^"+d.source+"$(?!\\s)",i.call(d))),c&&(e=d[s]),a=r.call(d,t),c&&a&&(d[s]=d.global?a.index+a[0].length:e),l&&a&&a.length>1&&o.call(a[0],n,(function(){for(h=1;h<arguments.length-2;h++)void 0===arguments[h]&&(a[h]=void 0)})),a}),t.exports=a},"52a7":function(t,e){e.f={}.propertyIsEnumerable},5537:function(t,e,n){var i=n("8378"),r=n("7726"),o="__core-js_shared__",a=r[o]||(r[o]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:i.version,mode:n("2d00")?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},"55dd":function(t,e,n){"use strict";var i=n("5ca1"),r=n("d8e8"),o=n("4bf8"),a=n("79e5"),s=[].sort,c=[1,2,3];i(i.P+i.F*(a((function(){c.sort(void 0)}))||!a((function(){c.sort(null)}))||!n("2f21")(s)),"Array",{sort:function(t){return void 0===t?s.call(o(this)):s.call(o(this),r(t))}})},"5ca1":function(t,e,n){var i=n("7726"),r=n("8378"),o=n("32e9"),a=n("2aba"),s=n("9b43"),c="prototype",l=function(t,e,n){var h,d,u,p,m=t&l.F,g=t&l.G,f=t&l.S,v=t&l.P,E=t&l.B,y=g?i:f?i[e]||(i[e]={}):(i[e]||{})[c],b=g?r:r[e]||(r[e]={}),x=b[c]||(b[c]={});for(h in g&&(n=e),n)d=!m&&y&&void 0!==y[h],u=(d?y:n)[h],p=E&&d?s(u,i):v&&"function"==typeof u?s(Function.call,u):u,y&&a(y,h,u,t&l.U),b[h]!=u&&o(b,h,p),v&&x[h]!=u&&(x[h]=u)};i.core=r,l.F=1,l.G=2,l.S=4,l.P=8,l.B=16,l.W=32,l.U=64,l.R=128,t.exports=l},"5cc5":function(t,e,n){var i=n("2b4c")("iterator"),r=!1;try{var o=[7][i]();o["return"]=function(){r=!0},Array.from(o,(function(){throw 2}))}catch(a){}t.exports=function(t,e){if(!e&&!r)return!1;var n=!1;try{var o=[7],s=o[i]();s.next=function(){return{done:n=!0}},o[i]=function(){return s},t(o)}catch(a){}return n}},"5dbc":function(t,e,n){var i=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var o,a=e.constructor;return a!==n&&"function"==typeof a&&(o=a.prototype)!==n.prototype&&i(o)&&r&&r(t,o),t}},"5df3":function(t,e,n){"use strict";var i=n("02f4")(!0);n("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=i(e,n),this._i+=t.length,{value:t,done:!1})}))},"5eda":function(t,e,n){var i=n("5ca1"),r=n("8378"),o=n("79e5");t.exports=function(t,e){var n=(r.Object||{})[t]||Object[t],a={};a[t]=e(n),i(i.S+i.F*o((function(){n(1)})),"Object",a)}},"5f1b":function(t,e,n){"use strict";var i=n("23c6"),r=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var o=n.call(t,e);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==i(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,e)}},"5fff":function(t,e,n){},"613b":function(t,e,n){var i=n("5537")("keys"),r=n("ca5a");t.exports=function(t){return i[t]||(i[t]=r(t))}},"61c8":function(t,e,n){},"622b":function(t,e,n){},"626a":function(t,e,n){var i=n("2d95");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==i(t)?t.split(""):Object(t)}},"628d":function(t,e,n){"use strict";n("5fff")},6762:function(t,e,n){"use strict";var i=n("5ca1"),r=n("c366")(!0);i(i.P,"Array",{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")("includes")},6821:function(t,e,n){var i=n("626a"),r=n("be13");t.exports=function(t){return i(r(t))}},"69a8":function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},"69bb":function(t,e,n){"use strict";n("ba05")},"6a2b":function(t,e,n){},"6a99":function(t,e,n){var i=n("d3f4");t.exports=function(t,e){if(!i(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!i(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},"6b54":function(t,e,n){"use strict";n("3846");var i=n("cb7c"),r=n("0bfb"),o=n("9e1e"),a="toString",s=/./[a],c=function(t){n("2aba")(RegExp.prototype,a,t,!0)};n("79e5")((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?c((function(){var t=i(this);return"/".concat(t.source,"/","flags"in t?t.flags:!o&&t instanceof RegExp?r.call(t):void 0)})):s.name!=a&&c((function(){return s.call(this)}))},"6fb5":function(t,e,n){},"718e":function(t,e,n){"use strict";n("e95c")},7333:function(t,e,n){"use strict";var i=n("9e1e"),r=n("0d58"),o=n("2621"),a=n("52a7"),s=n("4bf8"),c=n("626a"),l=Object.assign;t.exports=!l||n("79e5")((function(){var t={},e={},n=Symbol(),i="abcdefghijklmnopqrst";return t[n]=7,i.split("").forEach((function(t){e[t]=t})),7!=l({},t)[n]||Object.keys(l({},e)).join("")!=i}))?function(t,e){var n=s(t),l=arguments.length,h=1,d=o.f,u=a.f;while(l>h){var p,m=c(arguments[h++]),g=d?r(m).concat(d(m)):r(m),f=g.length,v=0;while(f>v)p=g[v++],i&&!u.call(m,p)||(n[p]=m[p])}return n}:l},7514:function(t,e,n){"use strict";var i=n("5ca1"),r=n("0a49")(5),o="find",a=!0;o in[]&&Array(1)[o]((function(){a=!1})),i(i.P+i.F*a,"Array",{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")(o)},7726:function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"77f1":function(t,e,n){var i=n("4588"),r=Math.max,o=Math.min;t.exports=function(t,e){return t=i(t),t<0?r(t+e,0):o(t,e)}},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"7a56":function(t,e,n){"use strict";var i=n("7726"),r=n("86cc"),o=n("9e1e"),a=n("2b4c")("species");t.exports=function(t){var e=i[t];o&&e&&!e[a]&&r.f(e,a,{configurable:!0,get:function(){return this}})}},"7f20":function(t,e,n){var i=n("86cc").f,r=n("69a8"),o=n("2b4c")("toStringTag");t.exports=function(t,e,n){t&&!r(t=n?t:t.prototype,o)&&i(t,o,{configurable:!0,value:e})}},"7f7f":function(t,e,n){var i=n("86cc").f,r=Function.prototype,o=/^\s*function ([^ (]*)/,a="name";a in r||n("9e1e")&&i(r,a,{configurable:!0,get:function(){try{return(""+this).match(o)[1]}catch(t){return""}}})},8378:function(t,e){var n=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=n)},"83a1":function(t,e,n){},"84f2":function(t,e){t.exports={}},"85ff":function(t,e,n){"use strict";n("cb50")},8615:function(t,e,n){var i=n("5ca1"),r=n("504c")(!1);i(i.S,"Object",{values:function(t){return r(t)}})},"86cc":function(t,e,n){var i=n("cb7c"),r=n("c69a"),o=n("6a99"),a=Object.defineProperty;e.f=n("9e1e")?Object.defineProperty:function(t,e,n){if(i(t),e=o(e,!0),i(n),r)try{return a(t,e,n)}catch(s){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},"8b97":function(t,e,n){var i=n("d3f4"),r=n("cb7c"),o=function(t,e){if(r(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:i(t,n),t}}({},!1):void 0),check:o}},"8bcf":function(t,e,n){},"8e6e":function(t,e,n){var i=n("5ca1"),r=n("990b"),o=n("6821"),a=n("11e9"),s=n("f1ae");i(i.S,"Object",{getOwnPropertyDescriptors:function(t){var e,n,i=o(t),c=a.f,l=r(i),h={},d=0;while(l.length>d)n=c(i,e=l[d++]),void 0!==n&&s(h,e,n);return h}})},"8fb6":function(t,e,n){"use strict";n("83a1")},9093:function(t,e,n){var i=n("ce10"),r=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,r)}},9204:function(t,e){var n=Object.defineProperty,i=(t,e,i)=>e in t?n(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,r=(t,e,n)=>(i(t,"symbol"!=typeof e?e+"":e,n),n);function o(){const t=navigator.userAgent,e=/(?:Windows Phone)/.test(t),n=/(?:SymbianOS)/.test(t)||e,i=/(?:Android)/.test(t),r=/(?:Firefox)/.test(t),o=/(?:iPad|PlayBook)/.test(t)||i&&!/(?:Mobile)/.test(t)||r&&/(?:Tablet)/.test(t),a=/(?:iPhone)/.test(t)&&!o;return{isTablet:o,isPhone:a,isAndroid:i,isPc:!a&&!i&&!n}}function a(t,e=!1,n="block"){t&&(t.className=t.className.replace(/ chat-view-show| chat-view-hidden/g,""),e?(t.style.display=n,t.className+=" chat-view-show"):(t.className+=" chat-view-hidden",t.style.display="none"))}function s(t,e="block"){return t&&t.style.display===e}function c(t,e,n){t.classList[n?"add":"remove"](e)}(function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const t of document.querySelectorAll('link[rel="modulepreload"]'))n(t);new MutationObserver(t=>{for(const e of t)if("childList"===e.type)for(const t of e.addedNodes)"LINK"===t.tagName&&"modulepreload"===t.rel&&n(t)}).observe(document,{childList:!0,subtree:!0})}function e(t){const e={};return t.integrity&&(e.integrity=t.integrity),t.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),"use-credentials"===t.crossOrigin?e.credentials="include":"anonymous"===t.crossOrigin?e.credentials="omit":e.credentials="same-origin",e}function n(t){if(t.ep)return;t.ep=!0;const n=e(t);fetch(t.href,n)}})();const l=(t=50)=>new Promise(e=>{setTimeout(e,t)}),h=t=>"false"!==String(t)&&"null"!==String(t)&&"0"!==String(t),d=(t,e,n=!1)=>{let i;return function(...r){const o=this,a=()=>{i=null,n||t.apply(o,r)},s=n&&!i;clearTimeout(i),i=setTimeout(a,e),s&&t.apply(o,r)}},u=(t,e)=>{let n;return function(...i){const r=this;n||(t.apply(r,i),n=!0,setTimeout((function(){n=!1}),e))}},p=(t,e,n)=>(t=t.toLowerCase(),e=e.toLowerCase(),n=n.toLowerCase(),n=n.replace(/\s/g,""),!/[\p{P}\p{S}]/u.test(n)&&m(t,e||t,n)),m=(t,e,n)=>{if(!n)return!1;const{chinesePart:i,pinyinPart:r,numberPart:o}=g(n);if(i&&!t.startsWith(i)||o&&!t.includes(o))return!1;const a=e.replace(/\s+/g,"").toLowerCase();return!(r&&!v(r,a))},g=t=>{let e="",n="",i="",r=null;for(const o of t)f(o)?(r="chinese",e+=o):/[a-zA-Z]/.test(o)?(r="pinyin",n+=o.toLowerCase()):/\d/.test(o)?(r="number",i+=o):r=null;return{chinesePart:e,pinyinPart:n,numberPart:i,currentType:r}},f=t=>{const e=t.charCodeAt(0);return e>=19968&&e<=40959||e>=13312&&e<=19903||e>=131072&&e<=173791},v=(t,e)=>{let n=0;for(const i of e){if(n>=t.length)break;i===t[n]&&n++}return n===t.length},E=(t,e,n)=>t.getAttribute("data-set-richType")===e?t:n>0?E(t.parentElement,e,n-1):null,y=(t,e,n)=>t&&t[e]?t[e]:n,b='<svg class="check-empty-svg" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse fill="#f5f5f5" cx="32" cy="33" rx="32" ry="7"></ellipse><g fill-rule="nonzero" stroke="#d9d9d9"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" fill="#fafafa"></path></g></g></svg>',x='<svg class="empty-svg" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse fill="#f5f5f5" cx="32" cy="33" rx="32" ry="7"></ellipse><g fill-rule="nonzero" stroke="#d9d9d9"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" fill="#fafafa"></path></g></g></svg>',C='<svg class="icon-search" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M684.8 223.530667a326.272 326.272 0 0 1 24.96 433.621333c2.645333 2.133333 5.290667 4.48 7.850667 7.04L870.4 817.066667c24.789333 24.746667 32.896 56.832 18.133333 71.594666-14.762667 14.805333-46.848 6.656-71.637333-18.090666l-152.789333-152.832a106.282667 106.282667 0 0 1-7.210667-7.936 326.101333 326.101333 0 0 1-433.109333-25.173334c-127.445333-127.445333-127.573333-333.952-0.256-461.269333 127.36-127.36 333.866667-127.232 461.269333 0.213333zM275.328 275.114667a252.885333 252.885333 0 0 0 0.256 357.632 252.885333 252.885333 0 0 0 357.632 0.256 252.885333 252.885333 0 0 0-0.256-357.632 252.885333 252.885333 0 0 0-357.632-0.256z" fill="#9B9B9B"></path></svg>',T='<div class="ant-spin ant-spin-spinning" aria-live="polite" aria-busy="true"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>',D='<svg class="match-empty-svg" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 1)" fill="none" fill-rule="evenodd"><ellipse fill="#f5f5f5" cx="32" cy="33" rx="32" ry="7"></ellipse><g fill-rule="nonzero" stroke="#d9d9d9"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" fill="#fafafa"></path></g></g></svg>',w='<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M9.218 17.41 19.83 6.796a.99.99 0 1 1 1.389 1.415c-3.545 3.425-4.251 4.105-11.419 11.074a.997.997 0 0 1-1.375.018c-1.924-1.801-3.709-3.568-5.573-5.43a.999.999 0 0 1 1.414-1.413z"></path></svg>',I='<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="m20.23 8.653-7.795 9.685a1.2 1.2 0 0 1-1.87 0L2.771 8.652C2.14 7.867 2.698 6.7 3.706 6.7h15.588c1.008 0 1.567 1.167.935 1.952"></path></svg>';class k{constructor(t){r(this,"target"),r(this,"richText",document.createElement("div")),r(this,"placeholderElm",document.createElement("div")),r(this,"isExternalCallPopup",!1),r(this,"isPointSearchMode",!1),r(this,"checkboxRows",[]),r(this,"customTags",{}),r(this,"selectTags",{}),r(this,"pcElms",{containerDialogElm:null,pointDialogElm:null,pointDialogCheckElm:null,pointDialogMainElm:null,pointDialogUsersElm:[],pointDialogActiveElm:null,pointDialogLoadingElm:null,pointDialogEmptyElm:null,checkDialogElm:null,checkDialogSearchResultElm:null,checkDialogUsersElm:null,checkDialogSearchInputElm:null,checkDialogTagsElm:null,customTagDialogElms:{},customTagDialogTagKey:"",customTagDialogActiveElm:null,selectDialogElms:{},selectDialogKey:"",selectDialogAim:null}),r(this,"h5Elms",{dialogElm:null,dialogMainElm:null,dialogCheckElm:null,dialogShowElm:null,dialogSearchElm:null,dialogEmptyElm:null,dialogLoadingElm:null}),this.target=t,this.createRichText(),this.createPlaceholder(),t.deviceInfo.isPc?this.createPCDialog():this.createH5Dialog()}createRichText(){const{options:t,deviceInfo:e}=this.target,{elm:n}=t;c(n,"chat-area-"+(e.isPc?"pc":"h5"),!0),c(this.richText,"chat-rich-text",!0),this.richText.setAttribute("data-set-richType","richAllBox"),this.richText.setAttribute("contenteditable","true"),n.appendChild(this.richText)}createPlaceholder(){const{options:t}=this.target,{elm:e}=t;c(this.placeholderElm,"chat-placeholder-wrap",!0),a(this.placeholderElm,!0),e.appendChild(this.placeholderElm)}createPCDialog(){const{options:t}=this.target,{needDialog:e,elm:n,asyncMatch:i}=t;if(!e)return;const{pcElms:r}=this;if(r.containerDialogElm=document.createElement("div"),c(this.pcElms.containerDialogElm,"chat-dialog",!0),!n.parentElement)throw new Error('配置项："elm" 需要存在一个父级元素，请检查后重新配置！');n.nextElementSibling?n.parentElement.insertBefore(r.containerDialogElm,n.nextElementSibling):n.parentElement.appendChild(r.containerDialogElm),i||this.createPCCheckDialog(),this.createPCPointDialog()}createPCCheckDialog(){const{options:t}=this.target,e=this.target.options.dialogLabels.pcPCheckDialog,{pcElms:n}=this;n.checkDialogElm=document.createElement("div"),c(n.checkDialogElm,"checkbox-dialog",!0),a(n.checkDialogElm),n.checkDialogElm.innerHTML=`\n          <div class="checkbox-dialog-container">\n            <div class="checkbox-dialog-container-header">\n                <span>${e.title}</span>\n                <span class="checkbox-dialog-container-header-close">⛌</span>\n            </div>\n            <div class="checkbox-dialog-container-body">\n                <div class="checkbox-dialog-left-box">\n                    <div class="checkbox-dialog-search">\n                        <input class="checkbox-dialog-search-input" placeholder="${e.searchPlaceholder}" type="text">\n                        <div class="checkbox-dialog-search-group"></div>\n                    </div>\n                    <div class="checkbox-dialog-tags"></div>\n                    <div class="checkbox-dialog-option">\n                        <button class="checkbox-dialog-option-btn btn-submit disabled">${e.confirmLabel}</button>\n                        <button class="checkbox-dialog-option-btn btn-close">${e.cancelLabel}</button>\n                    </div>\n                </div>\n                <div class="checkbox-dialog-right-box">\n                    <div class="checkbox-dialog-right-box-title">${e.userTagTitle}</div>\n                    <div class="checkbox-dialog-check-group"></div>\n                </div>\n            </div>\n          </div>\n        `,n.containerDialogElm.appendChild(n.checkDialogElm),n.checkDialogUsersElm=n.checkDialogElm.querySelector(".checkbox-dialog-check-group"),n.checkDialogSearchResultElm=n.checkDialogElm.querySelector(".checkbox-dialog-search-group"),n.checkDialogSearchInputElm=n.checkDialogElm.querySelector(".checkbox-dialog-search-input"),n.checkDialogTagsElm=n.checkDialogElm.querySelector(".checkbox-dialog-tags");const i=()=>{a(n.checkDialogElm),c(document.body,"disable-scroll")};n.checkDialogElm.querySelector(".checkbox-dialog-container-header-close").onclick=i,n.checkDialogElm.querySelector(".btn-close").onclick=i;const r=n.checkDialogElm.querySelector(".btn-submit");r.onclick=async()=>{if(r.classList.contains("disabled"))return;const e=this.checkboxRows.map(e=>{const n=Object.create(null);return n[t.userProps.id]=e.id,n[t.userProps.name]=e.name,n});await this.target.batchSetTag(e),i()},a(n.checkDialogSearchResultElm),n.checkDialogSearchResultElm.onclick=t=>{t.stopPropagation()},n.checkDialogSearchInputElm.onclick=t=>{t.stopPropagation()};const o=d(t=>{const e=String(t.target.value||"").replace(/'/g,"").trim();if(!e)return void a(n.checkDialogSearchResultElm);const i=this.target.searchUserList(e).map(t=>t.id);Array.from(n.checkDialogSearchResultElm.children,(t,e)=>{if(e===n.checkDialogSearchResultElm.children.length-1)a(t,0===i.length);else{const e=t.getAttribute("data-set-id");a(t,-1!==i.indexOf(e),"flex")}}),a(n.checkDialogSearchResultElm,!0)},200);n.checkDialogSearchInputElm.oninput=o,n.checkDialogSearchInputElm.onfocus=o}createPCPointDialog(){const{pcElms:t,target:e}=this;t.pointDialogElm=document.createElement("div"),c(t.pointDialogElm,"call-user-dialog",!0),a(t.pointDialogElm);const n=document.createElement("div");c(n,"call-user-dialog-header",!0),n.innerHTML=`<span class="call-user-dialog-header-title">${e.options.dialogLabels.pcPointDialog.title}</span>`,t.pointDialogCheckElm=document.createElement("span"),c(t.pointDialogCheckElm,"call-user-dialog-header-check",!0),t.pointDialogCheckElm.innerText=e.options.dialogLabels.pcPointDialog.checkLabel,t.pointDialogCheckElm.onclick=()=>{this.target.showPCCheckDialog(),this.isExternalCallPopup=!1},n.appendChild(t.pointDialogCheckElm),t.pointDialogElm.appendChild(n),t.pointDialogMainElm=document.createElement("div"),c(t.pointDialogMainElm,"call-user-dialog-main",!0),t.pointDialogElm.appendChild(t.pointDialogMainElm),e.options.asyncMatch&&(t.pointDialogLoadingElm=document.createElement("div"),c(t.pointDialogLoadingElm,"call-user-dialog-loading",!0),t.pointDialogLoadingElm.innerHTML=T,t.pointDialogElm.appendChild(t.pointDialogLoadingElm),a(t.pointDialogLoadingElm),t.pointDialogEmptyElm=document.createElement("div"),c(t.pointDialogEmptyElm,"call-user-dialog-empty",!0),t.pointDialogEmptyElm.innerHTML=`\n                ${D}\n                <span class="empty-label">${e.options.dialogLabels.pcPointDialog.emptyLabel}</span>\n            `,t.pointDialogElm.appendChild(t.pointDialogEmptyElm),a(t.pointDialogEmptyElm)),t.containerDialogElm.appendChild(t.pointDialogElm)}createH5Dialog(){const{options:t,chatEvent:e}=this.target,{needDialog:n,dialogLabels:i}=t;if(!n)return;const{h5Elms:r}=this;r.dialogElm=document.createElement("div"),c(r.dialogElm,"call-user-popup",!0),r.dialogElm.innerHTML=`\n          <div class="call-user-popup-main">\n            <div class="call-user-popup-header">\n                <span class="popup-show">${i.h5Dialog.cancelLabel}</span>\n                <span class="popup-title">${i.h5Dialog.title}</span>\n                <span class="popup-check">${i.h5Dialog.confirmLabel}</span>\n            </div>\n            <div class="call-user-popup-search">\n                ${C}\n                <input class="call-user-popup-search-input"\n                       placeholder="${i.h5Dialog.searchPlaceholder}"\n                       type="text">\n            </div>\n            <div class="call-user-popup-body"></div>\n          </div>\n        `;const o=async()=>{r.dialogElm.className=r.dialogElm.className.replace(/ chat-view-show/g," chat-view-hidden"),r.dialogSearchElm.value="",await l(260),a(r.dialogElm),c(document.body,"disable-scroll"),t.asyncMatch&&this.target.updateUserList([]),this.target.chatInput.restCursorPos(this.target.chatInput.vnode,this.target.chatInput.cursorIndex),this.target.chatInput.viewIntoPoint()};r.dialogElm.onclick=o;const s=r.dialogElm.querySelector(".call-user-popup-main");s.onclick=t=>{t.stopPropagation()},r.dialogShowElm=r.dialogElm.querySelector(".popup-show"),r.dialogShowElm.onclick=o,r.dialogCheckElm=r.dialogElm.querySelector(".popup-check"),r.dialogCheckElm.onclick=async()=>{if(r.dialogCheckElm.classList.contains("disabled"))return;const e=r.dialogElm.querySelectorAll(".user-popup-check-item-check")||[];if(0===e.length)return void await o();if(Array.prototype.some.call(e,t=>"isALL"===t.getAttribute("data-set-id")))return await this.target.onceSetTag({[t.userProps.id]:"isALL",[t.userProps.name]:t.dialogLabels.h5Dialog.callEveryLabel}),void await o();const n=Array.from(e,t=>t.getAttribute("data-set-id")),i=t.userList.filter(e=>-1!==n.indexOf(String(e[t.userProps.id])));await this.target.batchSetTag(i),await o()},r.dialogMainElm=r.dialogElm.querySelector(".call-user-popup-body"),r.dialogEmptyElm=document.createElement("div"),c(r.dialogEmptyElm,"call-user-popup-empty",!0),this.h5Elms.dialogEmptyElm.innerHTML=`\n            ${x}\n            <span class="empty-label">${t.dialogLabels.h5Dialog.searchEmptyLabel}</span>\n        `,a(r.dialogEmptyElm),s.appendChild(r.dialogEmptyElm),t.asyncMatch&&(r.dialogLoadingElm=document.createElement("div"),c(r.dialogLoadingElm,"call-user-popup-loading",!0),r.dialogLoadingElm.innerHTML=T,a(r.dialogLoadingElm),s.appendChild(r.dialogLoadingElm)),r.dialogSearchElm=r.dialogElm.querySelector(".call-user-popup-search-input"),r.dialogSearchElm.oninput=d(n=>{const i=String(n.target.value||"").replace(/'/g,"").trim();if(t.asyncMatch){e.matchKey++;const t=e.matchKey;this.target.updateUserList([]),a(r.dialogLoadingElm,!0),a(r.dialogEmptyElm);const n=e.triggerChatEvent("atMatch",i).find(t=>t&&t instanceof Promise);return void(n&&n.then(n=>{if(t===e.matchKey){if(a(r.dialogLoadingElm),!n||n.length<=0)return void a(r.dialogEmptyElm,!0,"flex");this.target.updateUserList(n)}}))}const o=[];Array.from(this.h5Elms.dialogMainElm.children,t=>{if(!i)return a(t,!0,"flex"),void o.push(t);const e=t.getAttribute("data-set-name")||"",n=t.getAttribute("data-set-pinyin")||"";p(e,n,i)?(a(t,!0,"flex"),o.push(t)):a(t)}),a(this.h5Elms.dialogEmptyElm,!o.length,"flex")},200),a(r.dialogElm),document.body.appendChild(r.dialogElm)}updatePCUser(){const{pcElms:t,target:e}=this;t.pointDialogMainElm.innerHTML="",t.pointDialogActiveElm=void 0;const n=document.createDocumentFragment();if(this.target.options.needCallEvery){const t=document.createElement("div");c(t,"call-user-dialog-item",!0),t.setAttribute("data-set-id","isALL"),this.userSelectStyleAndEvent(t,{id:"isALL",name:e.options.dialogLabels.pcPointDialog.callEveryLabel}),t.innerHTML=`\n                <span class="call-user-dialog-item-sculpture">\n                  <span style="transform: scale(0.75)">@</span>\n                </span>\n                <span class="call-user-dialog-item-name">${e.options.dialogLabels.pcPointDialog.callEveryLabel}(${e.options.reformList.length})</span>\n            `,n.appendChild(t)}if(e.options.reformList.forEach(t=>{const e=document.createElement("div");c(e,"call-user-dialog-item",!0),e.setAttribute("data-set-id",t.id),this.userSelectStyleAndEvent(e,t),this.getUserHtmlTemplate(e,t),n.appendChild(e)}),t.pointDialogMainElm.appendChild(n),t.pointDialogUsersElm=[],Array.from(t.pointDialogMainElm.children||[],(e,n)=>{t.pointDialogUsersElm.push({index:n,elm:e})}),e.options.asyncMatch)return;t.checkDialogUsersElm.innerHTML=`\n            <div class="checkbox-dialog-check-item" data-set-value="ALL">\n                <input type="checkbox" value>\n                <span class="checkbox-dialog-check-item-inner"></span>\n                <div class="checkbox-dialog-check-item-label">${e.options.dialogLabels.pcPCheckDialog.checkAllLabel}</div>\n            </div>\n        `;const i=document.createDocumentFragment();e.options.reformList.forEach(t=>{const e=document.createElement("div");c(e,"checkbox-dialog-check-item",!0),e.setAttribute("data-set-value",t.id),e.innerHTML='\n                <input type="checkbox" value>\n                <span class="checkbox-dialog-check-item-inner"></span>\n            ',this.getUserHtmlTemplate(e,t),i.appendChild(e)}),t.checkDialogUsersElm.appendChild(i),t.checkDialogUsersElm&&t.checkDialogUsersElm.children.length&&Array.from(t.checkDialogUsersElm.children,t=>{t.onclick=()=>{const n=t.getAttribute("data-set-value")||"",i=e.options.reformList.find(t=>t.id===n),r=-1===t.className.indexOf("checkbox-dialog-check-item-check");"ALL"===n?this.checkboxRows=r?e.options.reformList.map(t=>t):[]:r?this.checkboxRows.push(i):this.checkboxRows=this.checkboxRows.filter(t=>t.id!==n),this.updateCheckDialogTags()}});const r=document.createDocumentFragment();e.options.reformList.forEach(t=>{const n=document.createElement("div");c(n,"checkbox-dialog-check-item",!0),n.setAttribute("data-set-id",t.id);const i=document.createElement("div");c(i,"checkbox-dialog-check-item-label",!0),this.getUserHtmlTemplate(i,t),n.appendChild(i),n.onclick=()=>{a(this.pcElms.checkDialogSearchResultElm);const t=n.getAttribute("data-set-id")||"";if(this.pcElms.checkDialogSearchInputElm.value="",this.pcElms.checkDialogSearchInputElm.focus(),this.checkboxRows.some(e=>e.id===t))return;const i=e.options.reformList.find(e=>e.id===t);i&&this.checkboxRows.push(i),this.updateCheckDialogTags()},r.appendChild(n)});const o=document.createElement("div");c(o,"checkbox-dialog-search-empty",!0),o.innerText=e.options.dialogLabels.pcPCheckDialog.searchEmptyLabel,r.appendChild(o),t.checkDialogSearchResultElm.appendChild(r)}updateH5User(){const{h5Elms:t,target:e}=this;t.dialogMainElm.innerHTML="";const n=e.options.reformList&&e.options.reformList.length>0,i=document.createDocumentFragment(),r=document.createElement("span");if(r.innerHTML='\n            <input type="checkbox" value>\n            <span class="user-popup-check-item-inner"></span>\n        ',n){const t=document.createElement("div");e.options.needCallEvery&&(c(t,"call-user-popup-item",!0),t.setAttribute("data-set-id","isALL"),t.innerHTML=`\n                    <span class="call-user-dialog-item-sculpture">\n                        <span style="transform: scale(0.75)">@</span>\n                    </span>\n                    <span class="call-user-dialog-item-name">${e.options.dialogLabels.h5Dialog.callEveryLabel}(${e.options.reformList.length})</span>\n                `,t.appendChild(r.cloneNode(!0)),t.onclick=()=>{const e=!t.classList.contains("user-popup-check-item-check");Array.from(this.h5Elms.dialogMainElm.children,t=>{c(t,"user-popup-check-item-check",e)}),c(this.h5Elms.dialogCheckElm,"disabled",!e)},i.appendChild(t)),e.options.reformList.forEach((e,n)=>{const o=document.createElement("div");c(o,"call-user-popup-item",!0),o.setAttribute("data-set-id",e.id),o.setAttribute("data-set-name",e.name),o.setAttribute("data-set-pinyin",e.pinyin||""),this.getUserHtmlTemplate(o,e),o.appendChild(r.cloneNode(!0)),i.appendChild(o),o.onclick=e=>{const n=!o.classList.contains("user-popup-check-item-check");c(o,"user-popup-check-item-check",n);const i=Array.prototype.every.call(this.h5Elms.dialogMainElm.children,t=>t.classList.contains("user-popup-check-item-check")||"isALL"===t.getAttribute("data-set-id"));c(t,"user-popup-check-item-check",i);const r=Array.prototype.some.call(this.h5Elms.dialogMainElm.children,t=>t.classList.contains("user-popup-check-item-check"));c(this.h5Elms.dialogCheckElm,"disabled",!r)}})}t.dialogMainElm.appendChild(i)}updateCheckDialogTags(){const t=this.checkboxRows.map(t=>t.id),e=[],n=[],i=document.createElement("div");i.className="check-empty",i.innerHTML=`\n            ${b}\n            <span class="check-empty-label">${this.target.options.dialogLabels.pcPCheckDialog.checkEmptyLabel}</span>\n        `,Array.from(this.pcElms.checkDialogTagsElm.children,i=>{const r=i.getAttribute("data-set-value");-1===t.indexOf(r)?n.push(i):e.push(r)}),Array.from(this.pcElms.checkDialogUsersElm.children,(e,n)=>{if(0===n)return void c(e,"checkbox-dialog-check-item-check",t.length===this.target.options.reformList.length);const i=e.getAttribute("data-set-value");c(e,"checkbox-dialog-check-item-check",-1!==t.indexOf(i))}),n.forEach(t=>{this.pcElms.checkDialogTagsElm.removeChild(t)});const r=this.pcElms.checkDialogElm.querySelector(".btn-submit");c(r,"disabled",t.length<=0),t.length||this.pcElms.checkDialogTagsElm.appendChild(i);const o=this.checkboxRows.filter(t=>-1===e.indexOf(t.id));if(!o.length)return;const a=document.createDocumentFragment();o.forEach(t=>{const e=document.createElement("div");e.setAttribute("class","checkbox-dialog-tag-item"),e.setAttribute("data-set-value",t.id),e.innerHTML=`\n        <span>${t.name}</span>\n      `;const n=document.createElement("span");n.setAttribute("class","checkbox-dialog-tag-item-close"),n.innerHTML="⛌",n.onclick=()=>{const t=e.getAttribute("data-set-value");this.checkboxRows=this.checkboxRows.filter(e=>e.id!==t),this.updateCheckDialogTags()},e.appendChild(n),a.appendChild(e)}),this.pcElms.checkDialogTagsElm.appendChild(a)}userSelectStyleAndEvent(t,e){t.addEventListener("click",async n=>{const{options:i}=this.target;if(n.stopPropagation(),this.updatePointActiveUserElm(t),this.isPointSearchMode||i.asyncMatch)await this.target.matchSetTag(e);else{const t=i.userList.find(t=>String(t[i.userProps.id])===e.id);await this.target.onceSetTag(t)}this.exitPointDialog()})}bindCustomTrigger(){Object.values(this.pcElms.customTagDialogElms).forEach(t=>{this.pcElms.containerDialogElm.removeChild(t)}),this.pcElms.customTagDialogElms={},this.customTags={},this.target.options.customTrigger.forEach(t=>{t.tagList&&t.tagList.length>0&&(this.customTags[t.prefix]=t.tagList.map(t=>({id:String(t.id),name:String(t.name),pinyin:String(t.pinyin||"")})),this.createCustomTagDialog(t))})}createCustomTagDialog(t){const e=document.createElement("div");e.setAttribute("class","call-tag-dialog"),a(e);const n=document.createElement("div");n.setAttribute("class","call-tag-dialog-header"),n.innerHTML=`<span class="call-tag-dialog-header-title">${t.dialogTitle||t.prefix}</span>`,e.appendChild(n);const i=document.createElement("div");i.setAttribute("class","call-tag-dialog-main"),t.tagList.forEach(t=>{const e=document.createElement("div");e.setAttribute("class","call-tag-dialog-item"),e.setAttribute("data-set-id",t.id);const n=document.createElement("span");n.setAttribute("class","call-tag-dialog-item-name"),n.innerHTML=t.name,e.appendChild(n),e.addEventListener("click",async n=>{n.stopPropagation(),this.updateActiveCustomTagElm(e),this.isPointSearchMode?await this.target.matchSetCustomTag(t):await this.target.onceSetCustomTag(t),this.exitCustomTagDialog()}),i.appendChild(e)}),e.appendChild(i),this.pcElms.containerDialogElm.appendChild(e),this.pcElms.customTagDialogElms[t.prefix]=e}getUserHtmlTemplate(t,e){const n=document.createElement("span");if(n.setAttribute("class","call-user-dialog-item-sculpture "+(e.avatar?"is-avatar":"")),e.avatar){const t=new Image;t.alt="",t.src=String(e.avatar),n.appendChild(t)}else n.innerHTML=`<span style="transform: scale(0.75)">${e.name.slice(-2)}</span>`;t.appendChild(n);const i=document.createElement("span");i.setAttribute("class","call-user-dialog-item-name"),i.innerHTML=e.name,t.appendChild(i)}updatePointActiveUserElm(t,e=!1){if(this.pcElms.pointDialogActiveElm&&c(this.pcElms.pointDialogActiveElm,"call-user-dialog-item-active"),this.pcElms.pointDialogActiveElm=t,t&&(c(t,"call-user-dialog-item-active",!0),e)){const e=Array.prototype.filter.call(this.pcElms.pointDialogMainElm.children,t=>-1===t.className.indexOf("user-no-match")),n=t.clientHeight,i=Array.prototype.indexOf.call(e,t),r=Math.ceil(Math.floor(this.pcElms.pointDialogMainElm.clientHeight/n)/2),o=i+1-r;this.pcElms.pointDialogMainElm.scrollTop=o>0?o*n:0}}updateActiveCustomTagElm(t,e=!1){if(this.pcElms.customTagDialogActiveElm&&c(this.pcElms.customTagDialogActiveElm,"call-tag-dialog-item-active"),this.pcElms.customTagDialogActiveElm=t,t&&(c(t,"call-tag-dialog-item-active",!0),e)){const e=this.pcElms.customTagDialogElms[this.pcElms.customTagDialogTagKey].children[1],n=Array.prototype.filter.call(e.children,t=>-1===t.className.indexOf("tag-no-match")),i=t.clientHeight,r=Array.prototype.indexOf.call(n,t),o=Math.ceil(Math.floor(e.clientHeight/i)/2),a=r+1-o;e.scrollTop=a>0?a*i:0}}showPointDialog(t){this.exitSelectDialog(),this.exitCustomTagDialog(),this.exitPointDialog(),this.isPointSearchMode=!!t;let e=null;this.pcElms.pointDialogUsersElm.forEach(n=>{const i=n.elm,r=i.getAttribute("data-set-id"),o=t&&t.every(t=>t.id!==r);!e&&!o&&(e=i),c(i,"user-no-match",o)}),null!==e&&this.updatePointActiveUserElm(e),a(this.pcElms.pointDialogCheckElm,!this.target.options.asyncMatch&&!this.isPointSearchMode),a(this.pcElms.pointDialogElm,!0),this.target.chatEvent.debounceEvents.dialogMoveToRange(this.pcElms.pointDialogElm),this.pcElms.pointDialogMainElm.scrollTop=0}showCustomTagDialog(t,e){this.exitSelectDialog(),this.exitCustomTagDialog(),this.exitPointDialog(),this.isPointSearchMode=!!e,this.pcElms.customTagDialogTagKey=t;const n=this.pcElms.customTagDialogElms[t],i=n.children[1];let r=null;Array.from(i.children,t=>{const n=t.getAttribute("data-set-id"),i=e&&e.every(t=>t.id!==n);!r&&!i&&(r=t),c(t,"tag-no-match",i)}),null!==r&&this.updateActiveCustomTagElm(r),a(n,!0),this.target.chatEvent.debounceEvents.dialogMoveToRange(n),n.children[1].scrollTop=0}exitPointDialog(){this.updatePointActiveUserElm(),this.target.options.asyncMatch&&this.target.updateUserList([]),a(this.pcElms.pointDialogElm)}exitCustomTagDialog(){this.updateActiveCustomTagElm();for(const t in this.pcElms.customTagDialogElms)a(this.pcElms.customTagDialogElms[t])}ruleShowPointDialog(){const{options:t,chatInput:e}=this.target;t.needDialog&&t.reformList.length>0&&e.showAt()&&(this.isExternalCallPopup=!1,this.showPointDialog())}showPlaceholder(){a(this.placeholderElm,this.target.isEmpty())}bindSelectList(){Object.values(this.pcElms.selectDialogElms).forEach(t=>{this.pcElms.containerDialogElm.removeChild(t)}),this.pcElms.selectDialogElms={},this.selectTags={},this.target.options.selectList.forEach(t=>{t.options&&t.options.length>0&&(this.selectTags[t.key]=t.options.map(t=>({id:String(t.id),name:String(t.name),preview:String(t.preview||"")})),this.createSelectDialog(t))})}createSelectDialog(t){const e=document.createElement("div");e.setAttribute("class","chat-select-dialog"),a(e);const n=document.createElement("div");n.setAttribute("class","chat-select-dialog-header"),n.innerHTML=`<span class="chat-select-dialog-header-title">${t.dialogTitle||t.key}</span>`,e.appendChild(n);const i=document.createElement("div");i.setAttribute("class","chat-select-dialog-main"),t.options.forEach(t=>{const e=document.createElement("div");if(e.setAttribute("class","chat-select-dialog-item"),e.setAttribute("data-set-id",t.id),t.preview){const n=document.createElement("img");n.setAttribute("class","chat-select-dialog-preview"),n.src=String(t.preview),e.appendChild(n)}const n=document.createElement("span");n.setAttribute("class","chat-select-dialog-name"),n.textContent=t.name;const r=document.createElement("span");r.setAttribute("class","chat-select-dialog-check"),r.innerHTML=w,a(r),n.appendChild(r),e.appendChild(n),e.onclick=async()=>{await this.target.setSelectTag(t)},i.appendChild(e)}),e.appendChild(i);const r=document.createElement("div");r.setAttribute("class","chat-select-arrow"),e.appendChild(r),this.pcElms.containerDialogElm.appendChild(e),this.pcElms.selectDialogElms[t.key]=e}exitSelectDialog(){for(const t in this.pcElms.selectDialogElms)a(this.pcElms.selectDialogElms[t]);this.pcElms.selectDialogKey="",this.pcElms.selectDialogAim&&(c(this.pcElms.selectDialogAim,"aim"),this.pcElms.selectDialogAim=null)}}class A{constructor(t){r(this,"target"),r(this,"richText"),r(this,"vnode"),r(this,"cursorIndex"),r(this,"cursorLeft"),r(this,"needCallSpace",!1),r(this,"VOID_KEY","\ufeff"),r(this,"ZERO_WIDTH_KEY","​"),r(this,"IME_RECORD",{MARK:void 0,GRID:void 0,TAG:void 0,NODE:void 0,INDEX:void 0}),this.target=t,this.richText=t.chatElement.richText,this.textInnerHtmlInit()}textInnerHtmlInit(t=!1,e){if(t||this.getNodeEmpty(this.richText)){this.richText.innerHTML="";const t=this.getGridElm();this.richText.appendChild(t);const n=t.children[0].children[0];e&&(n.textContent=e,n.setAttribute("data-set-empty","false"));const i=n.childNodes[0];this.restCursorPos(i,i.textContent===this.VOID_KEY?1:i.textContent.length)}}onceCall(t){return new Promise(e=>{const n=this.createChatTagElm(t,"@","at-user","user-id");this.replaceRegContent(n),e()})}onceSearchCall(t,e){return new Promise(n=>{const i=this.createChatTagElm(t,"@","at-user","user-id");this.replaceRegContent(i,e),n()})}onceCustomCall(t,e,n){return new Promise(i=>{const r=this.createChatTagElm(t,n,"at-tag","tag-id");r.children[0].setAttribute("data-set-prefix",n),this.replaceRegContent(r,e),i()})}upDataNodeOrIndex(){var t,e,n;const{focusNode:i,focusOffset:r,anchorOffset:o}=window.getSelection(),a=(null==i?void 0:i.parentNode)||void 0;!a||!a.getAttribute||"richInput"!==a.getAttribute("data-set-richType")||(null==(n=null==(e=null==(t=null==i?void 0:i.parentNode)?void 0:t.parentNode)?void 0:e.parentNode)?void 0:n.parentNode)!==this.richText||(this.vnode=i,this.cursorIndex=r,this.cursorLeft=o<r?o:r)}showAt(){if(this.upDataNodeOrIndex(),!this.vnode||this.vnode.nodeType!==Node.TEXT_NODE)return!1;const t=this.vnode.textContent||"",e=/@([^@\s]*)$/,n=t.slice(0,this.cursorIndex),i=e.exec(n);return i&&2===i.length&&"@"===n[n.length-1]}getRangeRect(){let t=0,e=0;const n=window.getSelection();if(n.focusNode.nodeType!==Node.TEXT_NODE)return null;const i=n.getRangeAt(0).getClientRects()[0];return i&&(t=i.x,e=i.y),{x:t,y:e}}createChatTagElm(t,e,n,i){const r=document.createElement("span");return r.className=n,r.setAttribute("data-"+i,String(t.id)),r.contentEditable="false",r.textContent=`${e}${t.name}${this.needCallSpace?" ":""}`,this.createNewDom(r)}createNewDom(t){const e=document.createElement("span");return e.className="chat-tag",e.setAttribute("contenteditable","false"),e.setAttribute("data-set-richType","chatTag"),t.className+=" chat-stat",e.appendChild(t),e}restCursorPos(t,e){null==e?e=t.textContent===this.VOID_KEY?1:0:e>t.textContent.length&&(e=t.textContent.length);const n=new Range;n.setStart(t,e),n.setEnd(t,e);const i=window.getSelection();i&&(this.vnode=t,this.cursorIndex=e,this.cursorLeft=e,i.removeAllRanges(),i.addRange(n))}replaceRegContent(t,e=!0){const n=this.vnode.textContent;let i;i="boolean"==typeof e?n.slice(0,e?this.cursorIndex-1:this.cursorIndex):n.slice(0,e-1),0===i.length?(this.vnode.parentElement.setAttribute("data-set-empty",!0),this.vnode.textContent=this.VOID_KEY):this.vnode.textContent=i;let r=n.slice(this.cursorIndex);const o=this.vnode.parentNode.parentNode,a=o.nextSibling;a?o.parentNode.insertBefore(t,a):o.parentNode.appendChild(t);const s=t.previousSibling.childNodes[0],c=s.childNodes[1];c&&s.removeChild(c);const l=this.getGridElm(!0),h=l.childNodes[0];r&&r!==this.VOID_KEY&&(h.setAttribute("data-set-empty","false"),h.innerHTML=r);const d=h.childNodes[1];t.nextSibling?(d&&h.removeChild(d),o.parentNode.insertBefore(l,t.nextSibling)):o.parentNode.appendChild(l),this.restCursorPos(h.childNodes[0])}batchReplaceRegContent(t=[],e=!0){return new Promise(n=>{let i=`<span data-set-richType="richMark"><span class="chat-grid-input chat-stat" data-set-richType="richInput" data-set-empty="true">${this.VOID_KEY}</span></span>`;t.forEach(t=>{i+=`<span class="chat-tag" contenteditable="false" data-set-richType="chatTag"><span class="at-user chat-stat" data-user-id="${t.id}" contentEditable="false">@${t.name}${this.needCallSpace?" ":""}</span></span><span data-set-richType="richMark"><span class="chat-grid-input chat-stat" data-set-richType="richInput" data-set-empty="true">${this.VOID_KEY}</span></span>`});const r=document.createElement("div");r.innerHTML=i,this.insetRangeGrid(r,e?1:0),n()})}switchRange(t){var e,n;let i,r,{focusNode:o,focusOffset:a}=window.getSelection();if(o.getAttribute&&"richInput"===o.getAttribute("data-set-richType")&&(o=o.childNodes[0]),o.nodeType===Node.TEXT_NODE){const s=o.textContent.length,c=o.parentNode.parentNode;switch(t){case"ArrowLeft":if(a>0&&o.textContent!==this.VOID_KEY){r=a-1,i=o;break}const t=null==(e=null==c?void 0:c.previousSibling)?void 0:e.previousSibling;if(t)i=t.childNodes[0].childNodes[0],r=i.textContent.length;else{const t=c.parentNode.previousSibling;t&&(i=t.lastChild.childNodes[0].childNodes[0],r=i.textContent.length)}break;case"ArrowRight":if(a<s&&o.textContent!==this.VOID_KEY){r=a+1,i=o;break}const l=null==(n=null==c?void 0:c.nextSibling)?void 0:n.nextSibling;if(l)i=l.childNodes[0].childNodes[0],r=i.textContent===this.VOID_KEY?1:0;else{const t=c.parentNode.nextSibling;t&&(i=t.childNodes[0].childNodes[0].childNodes[0],r=i.textContent===this.VOID_KEY?1:0)}break}}(r||0===r)&&this.restCursorPos(i,r)}getGridElm(t=!1){const e=document.createElement("span");if(e.setAttribute("data-set-richType","richMark"),e.innerHTML=`<span class="chat-grid-input chat-stat" data-set-richType="richInput" data-set-empty="true">${this.VOID_KEY}<br></span>`,t)return e;const n=document.createElement("p");return n.className="chat-grid-wrap",n.setAttribute("data-set-richType","richBox"),n.appendChild(e),n}updateGrid(){const t=window.getSelection(),e=t.focusNode;if(!e)return;const n=e.parentNode,i=n.getAttribute("data-set-richType");let r,o,a,s;switch(i){case"richAllBox":if(r=e.childNodes[t.focusOffset],!r||"chatTag"===r.getAttribute("data-set-richType")){const t=this.getGridElm(!0),n=t.children[0];r?(n.removeChild(n.childNodes[1]),e.insertBefore(t,r)):e.appendChild(t),this.restCursorPos(n.childNodes[0]);break}if("BR"===r.tagName){const t=this.getGridElm(!0),n=t.children[0];e.insertBefore(t,r),e.removeChild(r),this.restCursorPos(n.childNodes[0],n.childNodes[0].textContent.length)}break;case"richMark":const i=n.parentNode,c=Array.prototype.indexOf.call(i.childNodes,n);if(-1===c)break;if(0===c){const e=t.focusNode;e.setAttribute("data-set-empty","true"),e.innerHTML=this.VOID_KEY+"<br>",r=e.childNodes[0],this.restCursorPos(r,r.textContent.length);break}let l,h=n.previousSibling;"chatTag"===h.getAttribute("data-set-richType")?(l=h.previousSibling,i.removeChild(h),i.removeChild(n)):(l=n.previousSibling,i.removeChild(n)),r=l.childNodes[0].childNodes[0],r.textContent===this.VOID_KEY&&r.parentNode.appendChild(document.createElement("br")),this.restCursorPos(r,r.textContent.length);break;case"richInput":if(s=n.parentNode,a=s.parentNode,this.getNodeEmpty(n)){n.setAttribute("data-set-empty","true"),a.childNodes[a.childNodes.length-1]===s&&(n.innerHTML=this.VOID_KEY+"<br>"),r=n.childNodes[0],this.restCursorPos(r,r.textContent.length);break}if("true"===String(n.getAttribute("data-set-empty"))){n.setAttribute("data-set-empty","false"),r=n.childNodes[0],this.target.chatEvent.isIMEModel?(n.childNodes[1]&&n.removeChild(n.childNodes[1]),r.textContent===this.VOID_KEY&&n.setAttribute("data-set-empty","true")):n.textContent=r.textContent.replace(new RegExp(this.VOID_KEY,"g"),"");const t=n.childNodes[0];this.restCursorPos(t,t.textContent.length)}if(o=n.parentNode.nextSibling,o&&o.nodeType===Node.TEXT_NODE){let t=o.textContent,e=this.getGridElm(!0);e.childNodes[0].textContent=t,e.childNodes[0].setAttribute("data-set-empty","false"),o.parentNode.insertBefore(e,o),o.parentNode.removeChild(o),o=e}o&&"richMark"===o.getAttribute("data-set-richType")&&this.markMerge(n.parentNode,o);break}}getNodeEmpty(t){const e=new RegExp(`^(${this.ZERO_WIDTH_KEY}|<br>|${this.VOID_KEY})+$`);return!t.innerHTML||e.test(t.innerHTML)}setWrap(t=!0){const e=window.getSelection();let{focusNode:n,focusOffset:i}=e;if(n.nodeType!==Node.TEXT_NODE){if(!n.getAttribute||"richInput"!==n.getAttribute("data-set-richType"))return;n=n.childNodes[0]}const r=n.textContent.slice(i),o=n.parentNode.parentNode,a=o.parentNode,s=Array.prototype.indexOf.call(a.childNodes,o),c=Array.prototype.slice.call(a.childNodes,s+1),l=this.getGridElm();let h=l.children[0].children[0].childNodes[0],d=1;(r||c.length>0)&&h.parentNode.removeChild(h.parentNode.childNodes[1]),r&&r!==this.VOID_KEY&&(n.textContent=n.textContent.slice(0,i),h.textContent=(h.textContent+r).replace(new RegExp(this.VOID_KEY,"g"),()=>(d--,"")),h.parentElement.setAttribute("data-set-empty","false")),c.forEach(t=>{a.removeChild(t),l.appendChild(t)});const u=a.lastChild.childNodes[0],p=l.lastChild.childNodes[0];if(u.childNodes.length<=1){const t=u.childNodes[0];(!t.textContent||t.textContent===this.VOID_KEY)&&(u.innerHTML=this.VOID_KEY+"<br>",u.setAttribute("data-set-empty","true"))}if("richMark"!==p.parentElement.getAttribute("data-set-richType"))l.appendChild(this.getGridElm(!0));else if(p.childNodes.length<=1){const t=p.childNodes[0];(!t.textContent||t.textContent===this.VOID_KEY)&&(p.innerHTML=this.VOID_KEY+"<br>",p.setAttribute("data-set-empty","true"),h=l.children[0].children[0].childNodes[0])}a.nextSibling?this.richText.insertBefore(l,a.nextSibling):this.richText.appendChild(l),t&&(this.restCursorPos(h,h.textContent===this.VOID_KEY?1:d),this.viewIntoPoint())}selectRegionMerge(){const t=window.getSelection();if(t.isCollapsed||t.rangeCount<=0)return;const e=t.getRangeAt(0);if(e.startContainer.nodeType===Node.TEXT_NODE&&e.startContainer===e.endContainer){const t=e.startContainer;if(t.length===e.endOffset-e.startOffset){const e=t.parentNode,n=e.parentNode===e.parentNode.parentNode.lastChild;e.setAttribute("data-set-empty","true"),e.innerHTML="\ufeff"+(n?"<br>":""),this.restCursorPos(e.childNodes[0])}else e.deleteContents()}else if(e.commonAncestorContainer&&"richBox"===e.commonAncestorContainer.getAttribute("data-set-richType")){const t=e.startContainer.nodeType===Node.TEXT_NODE?e.startContainer.parentNode.parentNode:e.startContainer,n=e.endContainer.nodeType===Node.TEXT_NODE?e.endContainer.parentNode.parentNode:e.endContainer;e.deleteContents(),t.getAttribute("data-set-richType")===n.getAttribute("data-set-richType")&&this.markMerge(t,n)}else if(e.commonAncestorContainer===e.startContainer&&e.startContainer===e.endContainer)this.textInnerHtmlInit(!0);else{const t=t=>{if(t.nodeType===Node.TEXT_NODE)return t.parentNode.parentNode.parentNode;switch(t.getAttribute("data-set-richType")){case"richInput":return t.parentNode.parentNode;case"richMark":return t.parentNode;case"richBox":return t;default:return null}},n=t(e.startContainer),i=t(e.endContainer);if(!n||!i)return;e.deleteContents(),this.gridMerge(n,i)}return!0}gridElmMerge(){const t=window.getSelection(),{focusNode:e,focusOffset:n,isCollapsed:i}=t;if(n>1||!i)return!1;const r=(t,e)=>(t.parentNode===this.richText||t===t.parentNode.childNodes[0])&&(-1!==Array.prototype.indexOf.call(this.richText.childNodes,t)?t:!(e>=6)&&r(t.parentNode,e+1)),o=r(e,0);if(!o||o===this.richText.childNodes[0]||1===n&&"false"===o.children[0].children[0].getAttribute("data-set-empty"))return!1;const a=o.previousSibling;return this.gridMerge(a,o),!0}delMarkRule(){const t=window.getSelection(),e=t.focusNode,n=e.textContent,i=e.parentNode,r=i.parentNode,o=r.parentNode;if(!t.isCollapsed||"richInput"!==i.getAttribute("data-set-richType"))return!1;if(n&&1===n.length&&r!==o.childNodes[0]&&(0!==t.focusOffset||n===this.VOID_KEY)){if(n===this.VOID_KEY){const t=r.previousSibling.previousSibling;o.removeChild(r.previousSibling),o.removeChild(r);const e=t.childNodes[0],n=e.childNodes[0];n.textContent===this.VOID_KEY&&t===o.lastChild&&e.appendChild(document.createElement("br")),this.restCursorPos(n,n.textContent.length)}else{i.innerHTML=r===o.lastChild?this.VOID_KEY+"<br>":this.VOID_KEY,i.setAttribute("data-set-empty","true");const t=i.childNodes[0];this.restCursorPos(t,1)}return!0}if(0===t.focusOffset){const t=i.parentNode,e=null==t?void 0:t.previousSibling;return!(!e||"chatTag"!==e.getAttribute("data-set-richType"))&&(this.delTag(e),!0)}}delTag(t){const e=t.previousSibling,n=t.nextSibling;t.parentNode.removeChild(t),this.markMerge(e,n)}gridMerge(t,e,n=!0){"richMark"!==t.lastChild.getAttribute("data-set-richType")&&t.appendChild(this.getGridElm(!0)),"richMark"!==e.childNodes[0].getAttribute("data-set-richType")&&e.insertBefore(this.getGridElm(!0),e.childNodes[0]);const i=t.lastChild.childNodes[0],r=i.childNodes[0];let o=r.textContent.length;Array.from(e.childNodes,e=>{t.appendChild(e.cloneNode(!0))}),e.childNodes.length>1&&i.childNodes[1]&&i.removeChild(i.childNodes[1]);const a=i.parentNode.nextSibling;if(a){const e=a.children[0].childNodes[0];e&&e.textContent!==this.VOID_KEY&&(i.childNodes[1]&&i.removeChild(i.childNodes[1]),r.textContent=(r.textContent+e.textContent).replace(new RegExp(this.VOID_KEY,"g"),()=>(o--,"")),r.parentElement.setAttribute("data-set-empty","false")),t.removeChild(a)}""===r.textContent&&(r.textContent=this.VOID_KEY,r.parentNode.setAttribute("data-set-empty","true"),o=1),this.richText.removeChild(e),n&&(this.restCursorPos(r,o),this.viewIntoPoint())}markMerge(t,e){const n=t.children[0].childNodes[0];let i=n.textContent.length;if(e){const t=e.children[0].childNodes[0];t&&t.textContent!==this.VOID_KEY&&(n.textContent=(n.textContent+t.textContent).replace(new RegExp(this.VOID_KEY,"g"),()=>(i--,"")),n.parentElement.setAttribute("data-set-empty","false")),e.parentNode.removeChild(e)}""===n.textContent&&(n.textContent=this.VOID_KEY,n.parentNode.setAttribute("data-set-empty","true"),i=1);const r=t.parentNode;n.textContent===this.VOID_KEY&&t===r.lastChild&&(n.parentNode.appendChild(document.createElement("br")),n.parentNode.setAttribute("data-set-empty","true"),i=1),this.restCursorPos(n,i)}setCallSpace(t){this.needCallSpace=t}getWrapNode(t,e=!1){if(t.nodeType===Node.TEXT_NODE)return t.parentNode.parentNode.parentNode;const n=t.getAttribute("data-set-richType");if(e&&"chatTag"===n)return t.parentNode;switch(n){case"richInput":return t.parentNode.parentNode;case"richMark":return t.parentNode;case"richBox":return t}}getMarkNode(t,e=!1){if(t.nodeType===Node.TEXT_NODE)return t.parentNode.parentNode;const n=t.getAttribute("data-set-richType");if(e&&"chatTag"===n)return t;switch(n){case"richInput":return t.parentNode;case"richMark":return t}}getRichTextNodeIndex(t){const e=this.getMarkNode(t),n=e.parentNode;return e&&n?{gridIndex:Array.prototype.indexOf.call(this.richText.childNodes,n),markIndex:Array.prototype.indexOf.call(n.childNodes,e)}:{gridIndex:null,markIndex:null}}setWrapNodeByMark(t){const e=document.createElement("p");return e.className="chat-grid-wrap",e.setAttribute("data-set-richType","richBox"),Array.from(t,t=>{e.appendChild(t)}),e}setRangeLastText(){const t=this.richText.childNodes[this.richText.childNodes.length-1],e=t.childNodes[t.childNodes.length-1].children[0].childNodes[0];this.restCursorPos(e,e.textContent===this.VOID_KEY?1:e.textContent.length),this.viewIntoPoint()}viewIntoPoint(){const t=window.getSelection();if(t.rangeCount>0){const e=t.getRangeAt(0),n=this.getWrapNode(e.endContainer);if(!n)return;const i=this.richText.parentElement,{scrollHeight:r,clientHeight:o,scrollTop:a}=i;if(r<=o)return;const s=n.getBoundingClientRect().top-i.getBoundingClientRect().top+n.clientHeight+a,c=a,l=o+a;if(s<c||s>l){const t=s-o;i.scrollTo(0,t)}}}insetRangeGrid(t,e=0){const n=this.vnode.textContent,i=n.slice(0,this.cursorIndex-e);0===i.length?(this.vnode.parentElement.setAttribute("data-set-empty",!0),this.vnode.textContent=this.VOID_KEY):this.vnode.textContent=i;let r=n.slice(this.cursorIndex);const o=[],a=document.createDocumentFragment();Array.from(t.children).forEach((t,e)=>{o.push(t),0!==e&&a.appendChild(t)});const s=o[o.length-1].children[0];r&&r.length>0&&r!==this.VOID_KEY?(s.setAttribute("data-set-empty","false"),s.innerHTML=s.textContent+r):s.setAttribute("data-set-empty","true");const c=this.getMarkNode(this.vnode),l=c.parentElement,h=c.children[0],d=o[0].textContent;d&&d.length>0&&d!==this.VOID_KEY&&(h.setAttribute("data-set-empty","false"),h.innerHTML=(h.textContent+d).replace(new RegExp(this.VOID_KEY,"g"),"")),h.childNodes[1]&&h.removeChild(h.childNodes[1]),c.nextElementSibling?(s.childNodes[1]&&s.removeChild(s.childNodes[1]),l.insertBefore(a,c.nextElementSibling)):l.appendChild(a);const u=r&&r!==this.VOID_KEY?r.length:0;if(o.length>1){const t=s.childNodes[0];this.restCursorPos(t,t.textContent===this.VOID_KEY?1:t.textContent.length-u)}else{const t=h.childNodes[0];this.restCursorPos(t,t.textContent===this.VOID_KEY?1:t.textContent.length-u)}}insetRangeGrids(t){const e=document.createDocumentFragment();Array.from(t).forEach(t=>{e.appendChild(t)});const n=this.getWrapNode(this.vnode);this.restCursorPos(this.vnode,this.cursorIndex),this.setWrap(!1);const i=n.nextElementSibling;i&&this.richText.insertBefore(e,i),i&&i.previousElementSibling?(this.gridMerge(n,n.nextElementSibling,!1),this.gridMerge(i.previousElementSibling,i,!0)):this.gridMerge(n,n.nextElementSibling,!0)}getOffsetRange(t,e,n=!1){const i=e.children[0].childNodes[0],r=i.textContent;if(0===t)return{rangeNode:i,rangeIndex:n?r.length:r===this.VOID_KEY?1:0};if(r.length>=t)return{rangeNode:i,rangeIndex:n?r.length-t:t};let o,a;if(n?(o=!(!e.previousElementSibling||!e.previousElementSibling.previousElementSibling),a=!!e.parentElement.previousElementSibling):(o=!(!e.nextElementSibling||!e.nextElementSibling.nextElementSibling),a=!!e.parentElement.nextElementSibling),!o&&!a)return{rangeNode:i,rangeIndex:n?r.length===this.VOID_KEY?1:0:r.length===this.VOID_KEY?1:r.length};const s=t-r.length-1,c=n?o?e.previousElementSibling.previousElementSibling:e.parentElement.previousElementSibling.lastElementChild:o?e.nextElementSibling.nextElementSibling:e.parentElement.nextElementSibling.children[0];return this.getOffsetRange(s,c,n)}setIMERecord(){if(this.target.deviceInfo.isPc)return;const t=this.getMarkNode(this.vnode);if(!t)return this.IME_RECORD.MARK=null,this.IME_RECORD.TAG=null,void(this.IME_RECORD.GRID=null);this.target.chatEvent.isIMEModel&&t.children[0].childNodes[0].textContent===this.VOID_KEY&&(t.children[0].childNodes[0].textContent=this.VOID_KEY+this.VOID_KEY,this.restCursorPos(this.vnode,this.cursorIndex+1)),this.IME_RECORD.NODE=this.vnode,this.IME_RECORD.INDEX=this.cursorIndex,this.IME_RECORD.MARK=t,this.IME_RECORD.TAG=this.IME_RECORD.MARK.previousElementSibling,this.IME_RECORD.GRID=this.IME_RECORD.MARK.parentElement}handleIMEDelete(){const t=(this.richText.children[0]||{childNodes:[]}).childNodes[0];if(t&&t.getAttribute&&"richMark"===t.getAttribute("data-set-richType")){if(this.IME_RECORD.INDEX<=2&&this.IME_RECORD.TAG){const t=this.IME_RECORD.TAG.previousElementSibling;this.IME_RECORD.GRID.removeChild(this.IME_RECORD.TAG),this.markMerge(t,this.IME_RECORD.MARK)}this.upDataNodeOrIndex()}else this.textInnerHtmlInit(!0)}handleIMEWrap(){this.upDataNodeOrIndex()}}const S={device:"auto",needDialog:!0,needDebounce:!0,asyncMatch:!1,userList:[],reformList:[],placeholder:"",maxLength:void 0,copyType:["text"],uploadImage:void 0,needCallEvery:!0,needCallSpace:!1,userProps:{},customTrigger:[],dialogLabels:{pcPointDialog:{},pcPCheckDialog:{},h5Dialog:{}},wrapKeyFun:t=>t.ctrlKey&&["Enter"].includes(t.key),sendKeyFun:t=>!t.ctrlKey&&["Enter"].includes(t.key)},L={id:"id",name:"name",avatar:"avatar",pinyin:"pinyin"},O={title:"群成员",callEveryLabel:"所有人",checkLabel:"多选",emptyLabel:"暂无数据"},_={title:"选择要@的人",searchPlaceholder:"搜素人员名称",searchEmptyLabel:"没有匹配到任何结果",userTagTitle:"研讨成员列表",checkAllLabel:"全选",checkEmptyLabel:"请选择需要@的成员",confirmLabel:"确定",cancelLabel:"取消"},M={title:"选择提醒的人",callEveryLabel:"所有人",searchPlaceholder:"搜素人员名称",searchEmptyLabel:"没有匹配到任何结果",confirmLabel:"确定",cancelLabel:"收起"},N={needUserId:!1,needTagId:!1,needSelectId:!1,wrapClassName:void 0,rowClassName:void 0,imgToText:!1,identifyLink:!1},P=["Backspace","Shift","Tab","CapsLock","Control","Meta","Alt","ContextMenu","Enter","NumpadEnter","Escape","ArrowLeft","ArrowUp","ArrowRight","ArrowDown","Home","End","PageUp","PageDown","Insert","Delete","NumLock"],R={"!":"！",$:"￥","(":"（",")":"）","[":"【","]":"】","-":"——",";":"；",":":"：","\\":"、","'":"’",'"':"“","`":"·",",":"，","<":"《",".":"。",">":"》","?":"？"};class j{constructor(t){r(this,"target"),r(this,"outerApply",!1),r(this,"isComposition",!1),r(this,"matchKey",0),r(this,"startOpenIndex",0),r(this,"textLength",0),r(this,"isIMEModel",!1),r(this,"undoHistory",[]),r(this,"redoHistory",[]),r(this,"doOverHistory",!0),r(this,"notMergeKey",P),r(this,"tagProps",R),r(this,"chatEventModule",{enterSend:[],operate:[],defaultAction:[],atMatch:[],atCheck:[],tagCheck:[],selectCheck:[]}),r(this,"debounceEvents",{recordHistory:()=>{},dialogMoveToRange:t=>{},selectDialogToAim:()=>{},matchPointDialog:()=>{},movePointActiveUserElm:t=>{},moveCustomActiveTagElm:t=>{}}),this.target=t,this.registerEvent(),this.otherEvent()}registerEvent(){const{chatElement:t,options:e,deviceInfo:n,chatInput:i}=this.target;t.richText.addEventListener("keyup",r=>{if(!e.needDialog)return;if(r.stopPropagation(),n.isPc)return void(50===r.keyCode||"Digit2"===r.code||"@"===r.key?t.ruleShowPointDialog():-1!==Object.keys(t.pcElms.customTagDialogElms).indexOf(r.key)&&t.showCustomTagDialog(r.key));const o="Unidentified"===r.key?"android":"ios";let a=!1;switch(o){case"android":a=229===r.keyCode;break;case"ios":a=50===r.keyCode||"Digit2"===r.code||"@"===r.key;break}a&&(e.reformList.length>0||e.asyncMatch)&&i.showAt()&&(this.target.showH5Dialog(),t.isExternalCallPopup=!1)}),t.richText.addEventListener("keydown",async r=>{if(!n.isPc&&"Unidentified"===r.key&&229===r.keyCode)return this.isIMEModel=!0,void i.setIMERecord();if(!this.isIMEModel){if(s(t.pcElms.pointDialogElm))return void(["ArrowUp","ArrowDown","Enter","NumpadEnter"].includes(r.code)?r.preventDefault():["ArrowLeft","ArrowRight"].includes(r.code)&&t.exitPointDialog());if(t.pcElms.customTagDialogTagKey&&s(t.pcElms.customTagDialogElms[t.pcElms.customTagDialogTagKey]))return void(["ArrowUp","ArrowDown","Enter","NumpadEnter"].includes(r.code)?r.preventDefault():["ArrowLeft","ArrowRight"].includes(r.code)&&t.exitCustomTagDialog());"Backspace"===r.code||"Backspace"===r.key?(i.selectRegionMerge()||i.gridElmMerge()||i.delMarkRule())&&(r.preventDefault(),await this.richTextInput()):e.wrapKeyFun(r)||!n.isPc&&"Enter"===r.key?(r.preventDefault(),i.setWrap(),await this.richTextInput()):e.sendKeyFun(r)?(r.preventDefault(),await l(100),this.triggerChatEvent("enterSend")):["ArrowLeft","ArrowRight"].includes(r.code)?(r.preventDefault(),i.switchRange(r.code)):r.ctrlKey&&"KeyA"===r.code?this.target.isEmpty()&&r.preventDefault():r.ctrlKey&&"KeyZ"===r.code?(r.preventDefault(),this.ruleChatEvent(()=>{this.target.undo()},"defaultAction","UNDO")):r.ctrlKey&&"KeyY"===r.code&&(r.preventDefault(),this.ruleChatEvent(()=>{this.target.redo()},"defaultAction","REDO")),-1===this.notMergeKey.indexOf(r.key)&&!r.ctrlKey&&!r.altKey&&!r.metaKey&&i.selectRegionMerge()}}),t.richText.addEventListener("input",async r=>{if(this.isIMEModel)return await l(50),"deleteContentBackward"===r.inputType?i.handleIMEDelete():"insertParagraph"===r.inputType?i.handleIMEWrap():(i.upDataNodeOrIndex(),this.isComposition||i.updateGrid()),void 0!==e.maxLength&&this.ruleMaxLength(),t.showPlaceholder(),this.triggerChatEvent("operate"),void(this.isIMEModel=!1);await this.richTextInput(),n.isPc&&!this.isComposition&&this.debounceEvents.matchPointDialog()}),t.richText.addEventListener("copy",t=>{t.preventDefault(),this.ruleChatEvent(()=>{this.copyRange(t)},"defaultAction","COPY")}),t.richText.addEventListener("cut",t=>{t.preventDefault(),this.ruleChatEvent(()=>{this.copyRange(t),this.removeRange()},"defaultAction","CUT")}),t.richText.addEventListener("paste",t=>{t.preventDefault();const{options:e,chatInput:n}=this.target;this.ruleChatEvent(()=>{const i=t.clipboardData.getData("text/plain");if("string"==typeof i&&""!==i){if(-1===e.copyType.indexOf("text"))return;let r=document.createElement("div");r.innerHTML=t.clipboardData.getData("application/my-custom-format")||"",n.selectRegionMerge(),r.children[0]&&"richBox"===r.children[0].getAttribute("data-set-richType")?this.insertInsideHtml(r.innerHTML):(r.innerHTML=i,this.target.insertText(r.innerText)),r=null}else{if(-1===e.copyType.indexOf("image"))return;const n=(t.clipboardData||t.originalEvent.clipboardData).items||[];Array.from(n,async t=>{if(-1===t.type.indexOf("image"))return;const n=t.getAsFile();if(e.uploadImage){const t=await e.uploadImage(n);this.target.insertHtml(`<img class="chat-img" src="${t}" alt="" />`)}else{const t=new FileReader;t.onload=t=>{this.target.insertHtml(`<img class="chat-img" src="${t.target.result}" alt="" />`)},t.readAsDataURL(n)}})}},"defaultAction","PASTE")}),t.richText.addEventListener("blur",()=>{i.upDataNodeOrIndex(),i.setIMERecord()}),t.richText.addEventListener("focus",()=>{i.upDataNodeOrIndex(),i.setIMERecord()}),t.richText.addEventListener("click",t=>{i.upDataNodeOrIndex(),i.setIMERecord();const e=E(t.target,"chatTag",3);if(e){const t=e.children[0];if(t.classList.contains("at-select")){const e=t.getAttribute("data-select-key");this.target.showPCSelectDialog(e,t)}}}),t.richText.addEventListener("dragstart",t=>{t.stopPropagation(),t.preventDefault()}),t.richText.addEventListener("dragover",t=>{t.stopPropagation(),t.preventDefault()}),t.richText.addEventListener("drop",t=>{t.stopPropagation(),t.preventDefault()}),t.richText.addEventListener("compositionstart",()=>{this.isComposition=!0}),t.richText.addEventListener("compositionend",()=>{this.isComposition=!1}),window.addEventListener("click",this.winClick.bind(this)),window.addEventListener("keydown",this.winKeydown.bind(this))}otherEvent(){const{options:t,chatInput:e,chatElement:n}=this.target,{needDebounce:i}=t,r=()=>{const{gridIndex:t,markIndex:i}=e.getRichTextNodeIndex(e.vnode);if(null===t||null==i)return;const r={html:n.richText.innerHTML,gridIndex:t,markIndex:i,cursorIndex:e.cursorIndex};this.undoHistory.push(r),this.undoHistory.length>50&&this.undoHistory.shift()};this.debounceEvents.recordHistory=i?d(r,200):r;const o=t=>{let i="0",r="100%";const o=e.getRangeRect();if(!o)return;const a=n.pcElms.containerDialogElm.getBoundingClientRect();let s=o.x-a.x,c=a.y-o.y;const{clientWidth:l,clientHeight:h}=t;o.x>window.innerWidth-l-30&&(s=o.x-l-a.x-16,i="100%"),o.y<h&&(c-=h,r="0"),t.style.transform="translate(0, 0)",t.style.transformOrigin=`${i} ${r}`,t.style.left=s+6+"px",t.style.bottom=c+"px",t.style.opacity="1"};this.debounceEvents.dialogMoveToRange=i?d(o,120,!0):o;const s=()=>{if(!t.needDialog)return;const i=e.vnode.textContent||"",r=e.cursorIndex,o=i.slice(0,r);let s=-1,c=-1,l="userTag";-1!==o.lastIndexOf("@")&&(s=o.lastIndexOf("@")),n.pcElms.customTagDialogTagKey&&-1!==o.lastIndexOf(n.pcElms.customTagDialogTagKey)&&(c=o.lastIndexOf(n.pcElms.customTagDialogTagKey));const h=this.tagProps[n.pcElms.customTagDialogTagKey];if(h&&-1!==o.lastIndexOf(h)&&(c=o.lastIndexOf(h)),c>s&&(l="customTag"),"userTag"===l&&t.asyncMatch){if(s<0)return void n.exitPointDialog();this.matchKey++;const t=this.matchKey;this.startOpenIndex=s+1;const e=o.slice(this.startOpenIndex)||"";if(/\s/gi.test(e))return void n.exitPointDialog();this.target.updateUserList([]),a(n.pcElms.pointDialogLoadingElm,!0,"flex"),a(n.pcElms.pointDialogEmptyElm),n.showPointDialog();const i=this.triggerChatEvent("atMatch",e).find(t=>t&&t instanceof Promise);return void(i&&i.then(e=>{if(t===this.matchKey){if(a(n.pcElms.pointDialogLoadingElm),!e||e.length<=0)return void a(n.pcElms.pointDialogEmptyElm,!0,"flex");this.target.updateUserList(e),n.pcElms.pointDialogUsersElm&&n.pcElms.pointDialogUsersElm.length>0&&n.updatePointActiveUserElm(n.pcElms.pointDialogUsersElm[0].elm)}}))}if("userTag"===l&&t.reformList.length<=0||"customTag"===l&&n.customTags[n.pcElms.customTagDialogTagKey].length<=0)return;const d=()=>{"userTag"===l?n.exitPointDialog():n.exitCustomTagDialog()},u=()=>{"userTag"===l?n.showPointDialog():n.showCustomTagDialog(n.pcElms.customTagDialogTagKey)};if(s<0&&c<0)return n.exitPointDialog(),void n.exitCustomTagDialog();this.startOpenIndex="userTag"===l?s+1:c+1;const m=new RegExp(`^([${e.ZERO_WIDTH_KEY}${e.VOID_KEY}])+$`);if(!o||m.test(o)||r<this.startOpenIndex)return void d();const g=o.slice(this.startOpenIndex)||"";if(/\s/gi.test(g))d();else if(g)if("userTag"===l){const t=this.target.searchUserList(g);t.length>0?n.showPointDialog(t):d()}else{const t=n.customTags[n.pcElms.customTagDialogTagKey].filter(t=>p(t.name,t.pinyin||"",g));t.length>0?n.showCustomTagDialog(n.pcElms.customTagDialogTagKey,t):d()}else u()};this.debounceEvents.matchPointDialog=i?d(s,200):s;const l=t=>{if(!n.pcElms.pointDialogActiveElm)return;let e=0;const i=n.pcElms.pointDialogActiveElm.getAttribute("data-set-id");n.pcElms.pointDialogUsersElm.some(t=>{const n=t.elm.getAttribute("data-set-id");return e=t.index,i===n});const r=n.pcElms.pointDialogUsersElm.filter(t=>!t.elm.classList.contains("user-no-match")),o=r.map(t=>t.index);let a;"down"===t?a=e===r[r.length-1].index?r[0]:r[o.indexOf(e)+1]:"up"===t&&(a=e===r[0].index?r[r.length-1]:r[o.indexOf(e)-1]),a&&n.updatePointActiveUserElm(a.elm,!0)};this.debounceEvents.movePointActiveUserElm=u(l,80);const h=t=>{if(!n.pcElms.customTagDialogActiveElm)return;const e=n.customTags[n.pcElms.customTagDialogTagKey].map(t=>t.id),i=n.pcElms.customTagDialogActiveElm.getAttribute("data-set-id"),r=e.indexOf(i),o=Array.from(n.pcElms.customTagDialogElms[n.pcElms.customTagDialogTagKey].children[1].children,(t,e)=>({elm:t,index:e})).filter(t=>!t.elm.classList.contains("tag-no-match")),a=o.map(t=>t.index);let s;"down"===t?s=r===o[o.length-1].index?o[0]:o[a.indexOf(r)+1]:"up"===t&&(s=r===o[0].index?o[o.length-1]:o[a.indexOf(r)-1]),s&&n.updateActiveCustomTagElm(s.elm,!0)};this.debounceEvents.moveCustomActiveTagElm=u(h,80);const m=()=>{const t=n.pcElms.selectDialogAim.getClientRects()[0],e=n.pcElms.selectDialogElms[n.pcElms.selectDialogKey];a(e,!0);const i=e.querySelector(".chat-select-arrow");let r=e.clientHeight+16;if(r>t.y?(r=-(t.height+16),i.style.top="-16px",i.style.bottom="auto",i.style.transform="rotate(0deg)"):(i.style.transform="rotate(180deg)",i.style.bottom="-16px",i.style.top="auto"),window.innerWidth-t.x<e.clientWidth){const n=e.clientWidth-(window.innerWidth-t.x)-10;e.style.left="auto",e.style.right="10px",i.style.left="auto",i.style.right=n-i.clientWidth/2+t.width/2+"px"}else e.style.left=t.x+"px",e.style.right="auto",i.style.left=t.width/2-i.clientWidth/2+"px",i.style.right="auto";e.style.top=t.y+"px",e.style.transform=`translateY(${-r}px)`;const o=e.querySelector(".chat-select-dialog-main"),s=e.querySelectorAll(".chat-select-dialog-item");let l=0,h=!1,d=0;if(n.pcElms.selectDialogAim.classList.contains("at-select")){const t=n.pcElms.selectDialogAim.getAttribute("data-select-id");c(n.pcElms.selectDialogAim,"aim",!0),Array.from(s,e=>{const n=e.lastChild.lastChild,i=t===e.getAttribute("data-set-id");i&&(d=e.clientHeight,h=!0),!i&&!h&&(l+=e.clientHeight),a(n,i,"inline-block")});const e=l-o.clientHeight/2+d/2;o.scrollTop=e>0?e:0}else Array.from(s,t=>{const e=t.lastChild.lastChild;a(e,!1,"inline-block")})};this.debounceEvents.selectDialogToAim=i?d(m,120):m;const g={html:n.richText.innerHTML,gridIndex:0,markIndex:0,cursorIndex:e.cursorIndex};this.undoHistory=[g]}winClick(){if(!this.target||this.outerApply)return;const{chatElement:t}=this.target;s(t.pcElms.pointDialogElm)&&t.exitPointDialog(),t.pcElms.checkDialogSearchResultElm&&a(t.pcElms.checkDialogSearchResultElm),t.pcElms.customTagDialogTagKey&&s(t.pcElms.customTagDialogElms[t.pcElms.customTagDialogTagKey])&&t.exitCustomTagDialog(),t.pcElms.selectDialogKey&&s(t.pcElms.selectDialogElms[t.pcElms.selectDialogKey])&&t.exitSelectDialog()}async winKeydown(t){if(!this.target)return;const{chatElement:e,options:n}=this.target;if(t.ctrlKey&&"KeyZ"===t.code&&t.preventDefault(),!this.isComposition)if(s(e.pcElms.pointDialogElm)){if("ArrowDown"===t.code)return t.preventDefault(),void this.debounceEvents.movePointActiveUserElm("down");if("ArrowUp"===t.code)return t.preventDefault(),void this.debounceEvents.movePointActiveUserElm("up");if(("Enter"===t.code||"NumpadEnter"===t.code)&&e.pcElms.pointDialogActiveElm){t.preventDefault();const i=e.pcElms.pointDialogActiveElm.getAttribute("data-set-id");if(await l(100),e.isPointSearchMode||n.asyncMatch)await this.target.matchSetTag(n.reformList.find(t=>t.id===i));else{const t=n.userList.find(t=>String(t[n.userProps.id])===i);await this.target.onceSetTag(t)}e.exitPointDialog()}}else if(e.pcElms.customTagDialogTagKey&&s(e.pcElms.customTagDialogElms[e.pcElms.customTagDialogTagKey])){if("ArrowDown"===t.code)return t.preventDefault(),void this.debounceEvents.moveCustomActiveTagElm("down");if("ArrowUp"===t.code)return t.preventDefault(),void this.debounceEvents.moveCustomActiveTagElm("up");if(("Enter"===t.code||"NumpadEnter"===t.code)&&e.pcElms.customTagDialogActiveElm){t.preventDefault();const n=e.pcElms.customTagDialogActiveElm.getAttribute("data-set-id");await l(100);const i=e.customTags[e.pcElms.customTagDialogTagKey].find(t=>t.id===n);e.isPointSearchMode?await this.target.matchSetCustomTag(i):await this.target.onceSetCustomTag(i),e.exitCustomTagDialog()}}}async richTextInput(t=!0){const{chatInput:e,deviceInfo:n,chatElement:i,options:r}=this.target;e.upDataNodeOrIndex(),n.isPc&&e.selectRegionMerge(),await l(50),this.isComposition||e.updateGrid();const o=(i.richText.children[0]||{childNodes:[]}).childNodes[0];if(!o||!o.getAttribute||"richMark"!==o.getAttribute("data-set-richType"))return e.textInnerHtmlInit(!0),i.showPlaceholder(),void this.triggerChatEvent("operate");void 0!==r.maxLength&&this.ruleMaxLength(),i.showPlaceholder(),this.triggerChatEvent("operate"),t&&this.doOverHistory&&!this.isComposition&&this.debounceEvents.recordHistory(),e.viewIntoPoint()}ruleMaxLength(){const{options:t,chatElement:e}=this.target;if(this.target.isEmpty()||void 0===t.maxLength)return void(this.textLength=0);let n=0,i=0;const r=[];Array.prototype.some.call(e.richText.children,(e,o)=>{const{nodeInfos:a,nodeTextLength:s}=this.getGirdNodeTextInfo(e);if(n+=s,r.push(a),i=o,n>=t.maxLength)return!0});const o=[];Array.from(e.richText.children,(t,e)=>{e>i&&o.push(t)}),o.forEach(t=>e.richText.removeChild(t)),this.deepDelGirdText(r,n)}getGirdNodeTextInfo(t){const{chatInput:e}=this.target,n=[];let i=0;if(1===t.children.length&&t!==t.parentElement.children[0]){const r=t.children[0],o=(r.textContent||"").replace(new RegExp(e.VOID_KEY,"g"),"");i+=o.length||1,n[0]={node:r,textLength:o.length||1,type:"richMark"}}else Array.from(t.children,(t,r)=>{if("richMark"===t.getAttribute("data-set-richType")){const o=(t.textContent||"").replace(new RegExp(e.VOID_KEY,"g"),"");i+=o.length,n[r]={node:t,textLength:o.length,type:"richMark"}}else{const o=(t.textContent||"").replace(new RegExp(e.VOID_KEY,"g"),"");i+=o.length||1,n[r]={node:t,textLength:o.length||1,type:"chatTag"}}});return{nodeInfos:n,nodeTextLength:i}}deepDelGirdText(t,e){if(e>this.target.options.maxLength){const n=t[t.length-1];t.pop(),this.deepDelNode(n,t,e)}else this.textLength=e}deepDelNode(t,e,n){const i=t[0].node.parentElement;if(n>this.target.options.maxLength){let r=n-this.target.options.maxLength,o=t[t.length-1];if("richMark"===o.type)if(0===o.textLength||r>=o.textLength)i.removeChild(o.node),t.pop(),r-=o.textLength,o=t[t.length-1],i.removeChild(o.node),t.pop(),r-=o.textLength;else{const t=o.node.childNodes[0];t.textContent=t.textContent.slice(0,o.textLength-r),0===t.textContent&&(t.setAttribute("data-set-empty","true"),t.innerHTML=this.target.chatInput.VOID_KEY+"<br>"),r=0}else i.removeChild(o.node),t.pop(),r-=o.textLength;r>0?t.length>0?this.deepDelNode(t,e,r+this.target.options.maxLength):(this.target.chatElement.richText.appendChild(i),this.deepDelGirdText(e,r+this.target.options.maxLength)):(this.textLength=this.target.options.maxLength+r,this.target.enable())}}copyRange(t){const e=window.getSelection();if(e.isCollapsed||e.rangeCount<=0)return t.clipboardData.setData("application/my-custom-format",""),void t.clipboardData.setData("text/plain","");const{chatElement:n,chatInput:i}=this.target,r=e.toString()||"";let o=document.createElement("div");o.innerHTML=r;const a=o.innerText.replace(/\n\n/g,"\n");o=null,t.clipboardData.setData("text/plain",a);const s=e.anchorNode,c=e.focusNode;if(s===c&&s.nodeType===Node.TEXT_NODE){const n=s.textContent.slice(e.anchorOffset,e.focusOffset);return void t.clipboardData.setData("application/my-custom-format",n)}if(s===n.richText&&c===n.richText)return void t.clipboardData.setData("application/my-custom-format",n.richText.innerHTML);const l=i.getWrapNode(s,!0),h=i.getWrapNode(c,!0),d=i.getMarkNode(s,!0),u=i.getMarkNode(c,!0),p="richMark"===d.getAttribute("data-set-richType"),m="richMark"===u.getAttribute("data-set-richType"),g=Array.prototype.indexOf.call(l.childNodes,d),f=Array.prototype.indexOf.call(h.childNodes,u);if(l===h&&l.parentNode===n.richText){const n=g>f,r=Array.prototype.filter.call(l.childNodes,(t,e)=>n?e<g&&e>f:e>g&&e<f).map(t=>t.cloneNode(!0)),o=p?n?s.textContent.slice(0,e.anchorOffset):s.textContent.slice(e.anchorOffset):"",a=m?n?c.textContent.slice(e.focusOffset):c.textContent.slice(0,e.focusOffset):"",h=i.getGridElm(!0),d=i.getGridElm(!0);o&&(h.childNodes[0].innerHTML=o,h.childNodes[0].setAttribute("data-set-empty","false")),a&&(d.childNodes[0].innerHTML=a,d.childNodes[0].setAttribute("data-set-empty","false")),n?("richMark"!==r[0].getAttribute("data-set-richType")&&r.unshift(d),"richMark"!==r[r.length-1].getAttribute("data-set-richType")&&r.push(h)):("richMark"!==r[0].getAttribute("data-set-richType")&&r.unshift(h),"richMark"!==r[r.length-1].getAttribute("data-set-richType")&&r.push(d));let u=document.createElement("div");const v=i.setWrapNodeByMark(r);return u.appendChild(v),t.clipboardData.setData("application/my-custom-format",u.innerHTML),void(u=null)}if(l.parentNode===n.richText&&h.parentNode===n.richText){const r=Array.prototype.indexOf.call(n.richText.childNodes,l),o=Array.prototype.indexOf.call(n.richText.childNodes,h),a=r>o,d=Array.prototype.filter.call(n.richText.childNodes,(t,e)=>a?e<r&&e>o:e>r&&e<o).map(t=>t.cloneNode(!0)),u=p?a?s.textContent.slice(0,e.anchorOffset):s.textContent.slice(e.anchorOffset):"",v=m?a?c.textContent.slice(e.focusOffset):c.textContent.slice(0,e.focusOffset):"",E=i.getGridElm(!0),y=i.getGridElm(!0);u&&(E.childNodes[0].innerHTML=u,E.childNodes[0].setAttribute("data-set-empty","false")),v&&(y.childNodes[0].innerHTML=v,y.childNodes[0].setAttribute("data-set-empty","false"));const b=Array.prototype.filter.call(l.childNodes,(t,e)=>a?e<g:e>g).map(t=>t.cloneNode(!0)),x=Array.prototype.filter.call(h.childNodes,(t,e)=>a?e>f:e<f).map(t=>t.cloneNode(!0));if(a){b.push(E),x.unshift(y);const t=i.setWrapNodeByMark(b),e=i.setWrapNodeByMark(x);d.push(t),d.unshift(e)}else{b.unshift(E),x.push(y);const t=i.setWrapNodeByMark(b),e=i.setWrapNodeByMark(x);d.unshift(t),d.push(e)}let C=document.createElement("div");return Array.from(d,t=>{C.appendChild(t)}),t.clipboardData.setData("application/my-custom-format",C.innerHTML),void(C=null)}}async removeRange(){const{chatInput:t,chatElement:e}=this.target;window.getSelection().getRangeAt(0).deleteContents(),await l(50),t.updateGrid(),e.showPlaceholder()}async setChatHistory(t){const{chatElement:e,chatInput:n}=this.target;this.doOverHistory=!1;const{html:i,gridIndex:r,markIndex:o,cursorIndex:a}=t;e.richText.innerHTML=i;const s=e.richText.childNodes[r].childNodes[o].childNodes[0].childNodes[0];n.restCursorPos(s,a),await this.richTextInput(),this.doOverHistory=!0}async insertInsideHtml(t){const{chatInput:e}=this.target;let n=document.createElement("div");n.innerHTML=t,n.children.length&&(1===n.children.length?e.insetRangeGrid(n.children[0]):e.insetRangeGrids(n.children),n=null,await this.richTextInput())}triggerChatEvent(t,...e){const n=[];return this.chatEventModule[t].forEach(t=>{t&&n.push(t(...e))}),n}ruleChatEvent(t,e,...n){this.triggerChatEvent(e,...n).some(t=>t&&"PREVENT"===t)||(t&&t.bind(this)(),t=null)}}const $=function(t,e,n){return t.forEach(t=>{if(n in t){const i=e.indexOf(String(t[n]));-1!==i&&(e[i]=t)}}),e.filter(t=>t[n])};class H{constructor(t){switch(r(this,"options"),r(this,"deviceInfo",o()),r(this,"chatElement"),r(this,"chatInput"),r(this,"chatEvent"),this.options=Object.assign({},S,t),this.options.device=this.options.device.toLocaleLowerCase(),this.options.device){case"pc":this.deviceInfo.isPc=!0;break;case"h5":this.deviceInfo.isPc=!1;break}this.options.userProps=Object.assign({},L,t.userProps||{}),this.options.dialogLabels.pcPointDialog=Object.assign({},O,y(t.dialogLabels,"pcPointDialog",{})),this.options.dialogLabels.pcPCheckDialog=Object.assign({},_,y(t.dialogLabels,"pcPCheckDialog",{})),this.options.dialogLabels.h5Dialog=Object.assign({},M,y(t.dialogLabels,"h5Dialog",{})),this.chatElement=new k(this),this.chatInput=new A(this),this.chatEvent=new j(this),this.updateConfig(t);const e=this;Object.defineProperty(this,"richText",{get(){return e.chatElement.richText}}),Object.defineProperty(this,"textLength",{get(){return e.chatEvent.textLength}}),this.addEventListener("operate",()=>{this.chatInput.setIMERecord()}),requestAnimationFrame(()=>{this.chatElement.richText.focus()})}updateConfig(t){void 0!==t.copyType&&(this.options.copyType=t.copyType),t.userProps&&(this.options.userProps=Object.assign({},L,t.userProps)),void 0!==t.uploadImage&&(this.options.uploadImage=t.uploadImage),void 0!==t.placeholder&&(this.chatElement.placeholderElm.textContent=t.placeholder),void 0!==t.maxLength&&(this.options.maxLength=t.maxLength,this.chatEvent.ruleMaxLength()),(this.options.asyncMatch||void 0!==t.needCallEvery||t.userList)&&(this.options.needCallEvery=!this.options.asyncMatch&&h(t.needCallEvery),this.updateUserList(this.options.asyncMatch?[]:t.userList)),void 0!==t.needCallSpace&&this.chatInput.setCallSpace(h(t.needCallSpace)),void 0!==t.wrapKeyFun&&(this.options.wrapKeyFun=t.wrapKeyFun),void 0!==t.sendKeyFun&&(this.options.sendKeyFun=t.sendKeyFun),this.options.needDialog&&t.customTrigger&&this.deviceInfo.isPc&&(this.options.customTrigger=t.customTrigger,this.chatElement.bindCustomTrigger()),this.options.needDialog&&t.selectList&&this.deviceInfo.isPc&&(this.options.selectList=t.selectList,this.chatElement.bindSelectList())}updateUserList(t){const{options:e,chatElement:n}=this;if(t){e.userList=JSON.parse(JSON.stringify(t));const n={[e.userProps.id]:"isALL",[e.userProps.name]:""};e.userList.unshift(n),e.reformList=t.map((t,n)=>{const i=t[e.userProps.id];if(!i&&0!==i)throw new Error(`配置项userList：下标第${n}项${e.userProps.id}值异常！`);return{id:String(i),name:String(t[e.userProps.name]||""),avatar:String(t[e.userProps.avatar]||""),pinyin:String(t[e.userProps.pinyin]||"")}})}const i=e.userList[0];i&&"isALL"===i[e.userProps.id]&&(i[e.userProps.name]=this.deviceInfo.isPc?e.dialogLabels.pcPointDialog.callEveryLabel:e.dialogLabels.h5Dialog.callEveryLabel),e.needDialog&&(this.deviceInfo.isPc?n.updatePCUser():n.updateH5User())}searchUserList(t){return this.options.reformList.filter(e=>p(e.name,e.pinyin||"",t))}getReduceNode(t){const e=Object.assign({},N,t||{}),n=/(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g,i=this.chatElement.richText.cloneNode(!0).querySelectorAll(".chat-grid-wrap")||[],r=document.createElement("div");return e.wrapClassName&&(r.className=e.wrapClassName),Array.from(i,(t,i)=>{const o=t.querySelectorAll(".chat-stat")||[],a=document.createElement("p");e.rowClassName&&(a.className=e.rowClassName),Array.from(o,t=>{this.chatInput.getNodeEmpty(t)||(t.removeAttribute("data-set-richType"),t.removeAttribute("contenteditable"),t.removeAttribute("data-set-empty"),e.needUserId||t.removeAttribute("data-user-id"),e.needTagId||(t.removeAttribute("data-set-prefix"),t.removeAttribute("data-tag-id")),e.needSelectId||(t.removeAttribute("data-select-id"),t.removeAttribute("data-select-key")),e.imgToText&&t.firstChild&&"IMG"===t.firstChild.tagName&&(t.classList.add("img-to-text"),t.innerHTML=`[${t.firstChild.getAttribute("data-img-text")||"元素data-img-text未定义"}]`),e.identifyLink&&-1!==t.className.indexOf("chat-grid-input")&&(t.innerHTML=t.innerHTML.replace(n,t=>`<a class="chat-grid-link" href="${t}" target="_blank">${t}</a>`)),t.classList.contains("at-select")&&(t.classList.remove("aim"),t.removeChild(t.lastChild)),a.appendChild(t))}),a.innerHTML||(a.innerHTML="<br>"),r.appendChild(a)}),r}getText(t){let e="";const n=this.getReduceNode(t);return Array.from(n.children,(t,n)=>{e=e+(n>0?"\n":"")+t.textContent}),e}getHtml(t){return this.getReduceNode(t).innerHTML}async reverseAnalysis(t,e){if(!t)return;const n=document.createElement("div");n.innerHTML=t;const i=n.children;Array.from(i,t=>{t.className="chat-grid-wrap",t.setAttribute("data-set-richType","richBox");const e=t.children,n={},i=[];Array.from(e,(r,o)=>{if(-1!==r.className.indexOf("chat-grid-input")){const t=r.textContent||"";return r.className="",r.setAttribute("data-set-richType","richMark"),void(r.innerHTML=`<span class="chat-grid-input chat-stat" data-set-richType="richInput" data-set-empty="false">${t}</span>`)}if("BR"===r.tagName){const e=this.chatInput.getGridElm(!0);return t.removeChild(r),void t.appendChild(e)}const a=r.cloneNode(!0);a.setAttribute("contenteditable","false");const s=document.createElement("span");s.className="chat-tag",s.setAttribute("contenteditable","false"),s.setAttribute("data-set-richType","chatTag"),s.appendChild(a),n[o]=s,o!==e.length-1?-1===e[o+1].className.indexOf("chat-grid-input")&&i.push(o):i.push(o),0===o&&i.push(-1)});for(const a in n){const i=Number(a),r=n[a].lastChild;r.classList.contains("at-select")&&(r.innerHTML=`${r.textContent}${I}`),i===e.length-1?(t.removeChild(e[i]),t.appendChild(n[a])):(t.insertBefore(n[a],e[i+1]),t.removeChild(e[i]))}const r=[],o=t.children;i.forEach(t=>{t===o.length-1?r.push("isEnd"):r.push(o[t+1])}),r.forEach(e=>{const n=this.chatInput.getGridElm(!0);if("isEnd"===e)t.appendChild(n);else{const i=n.children[0];i.childNodes.length>1&&i.removeChild(i.childNodes[1]),t.insertBefore(n,e)}})}),e?(this.enable(),await this.chatEvent.insertInsideHtml(n.innerHTML)):(this.chatElement.richText.innerHTML=n.innerHTML,this.enable(),await this.chatEvent.richTextInput())}async insertHtml(t){if(!t)return;const e=document.createElement("span");e.innerHTML=t,e.className="chat-set-html";const n=this.chatInput.createNewDom(e);return this.chatInput.replaceRegContent(n,!1),await this.chatEvent.richTextInput(),n}async insertText(t){if(!t)return;const e=new RegExp(`[${this.chatInput.ZERO_WIDTH_KEY}|${this.chatInput.VOID_KEY}]`,"ig"),n=t.replace(e,"");if(!n)return;const i=n.split("\n");let r="";i.forEach(t=>{const e=""!==t;r+=`<p class="chat-grid-wrap" data-set-richtype="richBox"><span data-set-richtype="richMark"><span class="chat-grid-input chat-stat" data-set-richtype="richInput" data-set-empty="${e?"false":"true"}">${e?t:this.chatInput.VOID_KEY+"<br>"}</span></span></p>`}),await this.chatEvent.insertInsideHtml(r)}getCallUserList(){const t=this.chatElement.richText.querySelectorAll(".at-user");if(t&&t.length>0){const e=Array.from(t,t=>t.dataset.userId);return $(this.options.userList,e,this.options.userProps.id)}return[]}getCallUserTagList(){const t=this.chatElement.richText.querySelectorAll(".at-user");if(t&&t.length>0){const e=[];return Array.from(t,t=>{e.some(e=>e[this.options.userProps.id]===t.dataset.userId)||e.push({[this.options.userProps.id]:t.dataset.userId,[this.options.userProps.name]:t.textContent.slice(1)})}),e}return[]}getCustomTagList(){const t=Object.keys(this.chatElement.customTags),e={},n=this.chatElement.richText.querySelectorAll(".at-tag");return t.forEach(t=>{let i=Array.prototype.filter.call(n,e=>e.getAttribute("data-set-prefix")===String(t)).map(t=>t.getAttribute("data-tag-id"));i=i.filter((t,e)=>i.indexOf(t)===e),e[t]=$(this.chatElement.customTags[t],i,"id")}),e}getSelectTagList(){const t=Object.keys(this.chatElement.selectTags),e={},n=this.chatElement.richText.querySelectorAll(".at-select");return t.forEach(t=>{let i=Array.prototype.filter.call(n,e=>e.getAttribute("data-select-key")===String(t)).map(t=>t.getAttribute("data-select-id"));i=i.filter((t,e)=>i.indexOf(t)===e),e[t]=$(this.chatElement.selectTags[t],i,"id")}),e}async clear(t){this.chatInput.textInnerHtmlInit(!0,t);const e={html:this.chatElement.richText.innerHTML,gridIndex:0,markIndex:0,cursorIndex:this.chatInput.cursorIndex};this.chatEvent.undoHistory=[e],this.chatEvent.redoHistory=[],await this.chatEvent.richTextInput(!1)}isEmpty(t=!1){if((this.chatElement.richText.querySelectorAll(".chat-tag")||[]).length>0)return!1;const e=new RegExp(`^(${this.chatInput.ZERO_WIDTH_KEY}|<br>|${this.chatInput.VOID_KEY})+$`),n=this.chatElement.richText.querySelectorAll(".chat-grid-input")||[];return t?Array.prototype.every.call(n,t=>!t.innerHTML||!t.textContent||!t.textContent.trim()||e.test(t.innerHTML)):Array.prototype.every.call(n,t=>!t.innerHTML||!t.textContent||e.test(t.innerHTML))}dispose(){if(this.options.elm.removeChild(this.chatElement.richText),this.options.elm.removeChild(this.chatElement.placeholderElm),this.options.needDialog)if(this.deviceInfo.isPc){const t=this.chatElement.pcElms.containerDialogElm.parentElement;t&&t.removeChild(this.chatElement.pcElms.containerDialogElm)}else document.body.removeChild(this.chatElement.h5Elms.dialogElm)}showPCPointDialog(){this.options.needDialog&&(this.insertText("@"),this.options.asyncMatch&&a(this.chatElement.pcElms.pointDialogEmptyElm,!0,"flex"),this.chatEvent.outerApply=!0,this.chatElement.showPointDialog(),l(50).then(()=>{this.chatEvent.outerApply=!1}))}showPCCheckDialog(){!this.options.needDialog||this.options.asyncMatch||(this.chatEvent.winClick(),this.chatElement.checkboxRows=[],a(this.chatElement.pcElms.checkDialogElm,!0),c(document.body,"disable-scroll",!0),this.chatElement.pcElms.checkDialogTagsElm.scrollTop=0,this.chatElement.pcElms.checkDialogUsersElm.scrollTop=0,this.chatElement.pcElms.checkDialogSearchInputElm.value="",this.chatElement.updateCheckDialogTags(),this.chatElement.isExternalCallPopup=!0)}showPCCustomTagDialog(t){!this.options.needDialog||this.options.asyncMatch||(this.insertText(t),this.chatEvent.outerApply=!0,this.chatElement.showCustomTagDialog(t),l(50).then(()=>{this.chatEvent.outerApply=!1}))}showPCSelectDialog(t,e){this.chatElement.exitCustomTagDialog(),this.chatElement.exitPointDialog(),this.chatEvent.outerApply=!0,e&&(this.chatElement.exitSelectDialog(),this.chatElement.pcElms.selectDialogAim=e),this.chatElement.pcElms.selectDialogKey=t,this.chatEvent.debounceEvents.selectDialogToAim(),l(50).then(()=>{this.chatEvent.outerApply=!1})}showH5Dialog(){this.chatElement.richText&&this.chatElement.richText.blur(),Array.from(this.chatElement.h5Elms.dialogMainElm.children,t=>{t.style.display="",c(t,"user-popup-check-item-check")}),c(this.chatElement.h5Elms.dialogCheckElm,"disabled",!0),a(this.chatElement.h5Elms.dialogElm,!0),c(document.body,"disable-scroll",!0),this.options.asyncMatch&&a(this.chatElement.h5Elms.dialogEmptyElm,!0,"flex"),this.chatElement.h5Elms.dialogMainElm.scrollTop=0,this.chatElement.isExternalCallPopup=!0}disabled(){this.chatElement.richText.setAttribute("contenteditable","false"),c(this.chatElement.richText,"chat-rich-text-disabled",!0)}enable(){this.chatElement.richText.setAttribute("contenteditable","true"),c(this.chatElement.richText,"chat-rich-text-disabled"),this.chatInput.setRangeLastText()}async setUserTag(t){this.chatEvent.triggerChatEvent("atCheck",[t]);const e=this.chatInput.createChatTagElm({id:t[this.options.userProps.id],name:t[this.options.userProps.name]},"@","at-user","user-id");this.chatInput.replaceRegContent(e,!1),await this.chatEvent.richTextInput()}async setCustomTag(t,e){this.chatEvent.triggerChatEvent("tagCheck",t,e),await this.chatInput.onceCustomCall(t,!1,e),await this.chatEvent.richTextInput()}async setSelectTag(t,e){if(this.chatEvent.triggerChatEvent("selectCheck",t,e||this.chatElement.pcElms.selectDialogKey),this.chatElement.pcElms.selectDialogAim&&this.chatElement.pcElms.selectDialogAim.classList.contains("at-select")){const e=this.chatElement.pcElms.selectDialogAim.getAttribute("data-select-id"),n=this.chatElement.pcElms.selectDialogAim.parentElement.nextElementSibling.childNodes[0].childNodes[0];if(this.chatInput.restCursorPos(n),e===t.id)return;this.chatElement.pcElms.selectDialogAim.setAttribute("data-select-id",t.id),this.chatElement.pcElms.selectDialogAim.childNodes[0].textContent=t.name}else{const n=document.createElement("span");n.setAttribute("class","at-select"),n.setAttribute("data-select-key",e||this.chatElement.pcElms.selectDialogKey),n.setAttribute("data-select-id",t.id),n.innerHTML=`${t.name}${I}`;const i=this.chatInput.createNewDom(n);this.chatInput.replaceRegContent(i,!1)}await this.chatEvent.richTextInput()}async matchSetTag(t){this.chatEvent.triggerChatEvent("atCheck",[t]),await this.chatInput.onceSearchCall(t,this.chatEvent.startOpenIndex),await this.chatEvent.richTextInput()}async onceSetTag(t){this.chatEvent.triggerChatEvent("atCheck",[t]),await this.chatInput.onceCall({id:t[this.options.userProps.id],name:t[this.options.userProps.name]}),await this.chatEvent.richTextInput()}async batchSetTag(t){this.chatEvent.triggerChatEvent("atCheck",t);const e=[];for(let n=0;n<=t.length-1;)e.push({id:t[n][this.options.userProps.id],name:t[n][this.options.userProps.name]}),n++;await this.chatInput.batchReplaceRegContent(e,!this.chatElement.isExternalCallPopup),await this.chatEvent.richTextInput()}async onceSetCustomTag(t){this.chatEvent.triggerChatEvent("tagCheck",t,this.chatElement.pcElms.customTagDialogTagKey),await this.chatInput.onceCustomCall(t,!0,this.chatElement.pcElms.customTagDialogTagKey),await this.chatEvent.richTextInput()}async matchSetCustomTag(t){this.chatEvent.triggerChatEvent("tagCheck",t,this.chatElement.pcElms.customTagDialogTagKey),await this.chatInput.onceCustomCall(t,this.chatEvent.startOpenIndex,this.chatElement.pcElms.customTagDialogTagKey),await this.chatEvent.richTextInput()}async undo(){const{chatEvent:t}=this;if(!t.doOverHistory||!t.undoHistory||t.undoHistory.length<=1)return;const e=t.undoHistory[t.undoHistory.length-2],n=t.undoHistory[t.undoHistory.length-1];t.redoHistory.push(n),t.undoHistory.pop(),await t.setChatHistory(e)}async redo(){const{chatEvent:t}=this;if(!t.doOverHistory||!t.redoHistory||t.redoHistory.length<1)return;const e=t.redoHistory[t.redoHistory.length-1];t.redoHistory.pop(),t.undoHistory.push(e),await t.setChatHistory(e)}cursorMove(t){if(0===t)return void this.chatInput.restCursorPos(this.chatInput.vnode,this.chatInput.cursorIndex);const e=new RegExp(`[${this.chatInput.ZERO_WIDTH_KEY}|${this.chatInput.VOID_KEY}]`,"ig");if(t>0){const n=this.chatInput.vnode.textContent.replace(e,"").slice(this.chatInput.cursorIndex);if(n.length>=t)return this.chatInput.cursorIndex+=t,void this.chatInput.restCursorPos(this.chatInput.vnode,this.chatInput.cursorIndex);const i=this.chatInput.vnode.parentElement.parentElement,r=i.parentElement;let o=!(!i.nextElementSibling||!i.nextElementSibling.nextElementSibling);const a=!!r.nextElementSibling;if(!o&&!a)return this.chatInput.cursorIndex+=n.length,0===this.chatInput.cursorIndex&&(this.chatInput.cursorIndex=1),void this.chatInput.restCursorPos(this.chatInput.vnode,this.chatInput.cursorIndex);const s=t-n.length-1,c=o?i.nextElementSibling.nextElementSibling:r.nextElementSibling.children[0],{rangeNode:l,rangeIndex:h}=this.chatInput.getOffsetRange(s,c);this.chatInput.restCursorPos(l,h)}else if(t<0){let n=Math.abs(t);const i=this.chatInput.vnode.textContent.replace(e,"").slice(0,this.chatInput.cursorIndex);if(i.length>=n)return this.chatInput.cursorIndex-=n,void this.chatInput.restCursorPos(this.chatInput.vnode,this.chatInput.cursorIndex);const r=this.chatInput.vnode.parentElement.parentElement,o=r.parentElement,a=!(!r.previousElementSibling||!r.previousElementSibling.previousElementSibling),s=!!o.previousElementSibling;if(!a&&!s)return void this.chatInput.restCursorPos(this.chatInput.vnode);n=n-i.length-1;const c=a?r.previousElementSibling.previousElementSibling:o.previousElementSibling.lastElementChild,{rangeNode:l,rangeIndex:h}=this.chatInput.getOffsetRange(n,c,!0);this.chatInput.restCursorPos(l,h)}}async cursorDel(t){if(0===t)return void this.chatInput.restCursorPos(this.chatInput.vnode,this.chatInput.cursorIndex);const e=this.chatInput.vnode,n=this.chatInput.cursorIndex;this.cursorMove(t);const i=this.chatInput.vnode,r=this.chatInput.cursorIndex,o=document.createRange();t<0?(o.setStart(i,r),o.setEnd(e,n)):(o.setStart(e,n),o.setEnd(i,r));const a=window.getSelection();a.removeAllRanges(),a.addRange(o),(this.chatInput.selectRegionMerge()||this.chatInput.gridElmMerge()||this.chatInput.delMarkRule())&&await this.chatEvent.richTextInput()}async delUserTags(t){const e=t||this.options.userList.map(t=>t[this.options.userProps.id]),n=this.chatElement.richText.querySelectorAll(".at-user"),i=[];Array.from(n,t=>{const n=t.getAttribute("data-user-id");e.some(t=>String(t)===n)&&i.push(t.parentElement)});for(let r=0;r<i.length;){const t=i[r];this.chatInput.delTag(t),r++}this.enable(),await this.chatEvent.richTextInput()}async delCustomTags(t,e){const n=this.options.customTrigger.find(e=>e.prefix===t);if(!n||0===n.tagList.length)return;const i=e||n.tagList.map(t=>t.id),r=this.chatElement.richText.querySelectorAll(".at-tag"),o=[];Array.from(r,e=>{const n=e.getAttribute("data-set-prefix"),r=e.getAttribute("data-tag-id");n===t&&i.some(t=>String(t)===r)&&o.push(e.parentElement)});for(let a=0;a<o.length;){const t=o[a];this.chatInput.delTag(t),a++}this.enable(),await this.chatEvent.richTextInput()}async delSelectTags(t,e){const n=this.options.selectList.find(e=>e.key===t);if(!n||0===n.options.length)return;const i=e||n.options.map(t=>t.id),r=this.chatElement.richText.querySelectorAll(".at-select"),o=[];Array.from(r,e=>{const n=e.getAttribute("data-select-key"),r=e.getAttribute("data-select-id");n===t&&i.some(t=>String(t)===r)&&o.push(e.parentElement)});for(let a=0;a<o.length;){const t=o[a];this.chatInput.delTag(t),a++}this.enable(),await this.chatEvent.richTextInput()}addEventListener(t,e){this.chatEvent.chatEventModule[t].push(e)}removeEventListener(t,e){const n=this.chatEvent.chatEventModule[t],i=n.indexOf(e);-1!==i&&n.splice(i,1)}revisePCPointDialogLabel(t){this.options.needDialog&&(this.options.dialogLabels.pcPointDialog=Object.assign({},O,t||{}),this.chatElement.pcElms.pointDialogElm.querySelector(".call-user-dialog-header-title").textContent=this.options.dialogLabels.pcPointDialog.title,this.chatElement.pcElms.pointDialogCheckElm.textContent=this.options.dialogLabels.pcPointDialog.checkLabel,this.chatElement.pcElms.pointDialogEmptyElm&&(this.chatElement.pcElms.pointDialogEmptyElm.children[1].textContent=this.options.dialogLabels.pcPointDialog.emptyLabel),this.options.asyncMatch||this.updateUserList())}revisePCCheckDialogLabel(t){!this.options.needDialog||this.options.asyncMatch||(this.options.dialogLabels.pcPCheckDialog=Object.assign({},_,t||{}),this.chatElement.pcElms.checkDialogElm.querySelector(".checkbox-dialog-container-header").children[0].textContent=this.options.dialogLabels.pcPCheckDialog.title,this.chatElement.pcElms.checkDialogSearchInputElm.setAttribute("placeholder",this.options.dialogLabels.pcPCheckDialog.searchPlaceholder),this.chatElement.pcElms.checkDialogElm.querySelector(".checkbox-dialog-search-empty").textContent=this.options.dialogLabels.pcPCheckDialog.searchEmptyLabel,this.chatElement.pcElms.checkDialogElm.querySelector(".checkbox-dialog-right-box-title").textContent=this.options.dialogLabels.pcPCheckDialog.userTagTitle,this.chatElement.pcElms.checkDialogUsersElm.children[0].children[2].textContent=this.options.dialogLabels.pcPCheckDialog.checkAllLabel,this.chatElement.pcElms.checkDialogElm.querySelector(".btn-submit").textContent=this.options.dialogLabels.pcPCheckDialog.confirmLabel,this.chatElement.pcElms.checkDialogElm.querySelector(".btn-close").textContent=this.options.dialogLabels.pcPCheckDialog.cancelLabel)}reviseH5DialogLabel(t){this.options.needDialog&&(this.options.dialogLabels.h5Dialog=Object.assign({},M,t||{}),this.chatElement.h5Elms.dialogElm.querySelector(".popup-title").textContent=this.options.dialogLabels.h5Dialog.title,this.chatElement.h5Elms.dialogSearchElm.setAttribute("placeholder",this.options.dialogLabels.h5Dialog.searchPlaceholder),this.chatElement.h5Elms.dialogEmptyElm.children[1].textContent=this.options.dialogLabels.h5Dialog.searchEmptyLabel,this.chatElement.h5Elms.dialogCheckElm.textContent=this.options.dialogLabels.h5Dialog.confirmLabel,this.chatElement.h5Elms.dialogShowElm.textContent=this.options.dialogLabels.h5Dialog.cancelLabel,this.options.asyncMatch||this.updateUserList())}}if(!window)throw new Error("非web环境！");window.console&&window.console.log&&console.log(" %c ".concat("ChatArea"," %c v5.3.6 "),"background: #269AFF; color: #FFFFFF; padding: 4px 0; border-radius: 4px 0px 0px 4px; font-style: italic;","background: #FFFFFF; color: #269AFF; padding: 2px 0; border-radius: 0px 4px 4px 0px; font-style: italic; border: 2px solid #269AFF;"),window.ChatArea=H},"93f9":function(t,e,n){"use strict";n("323d")},"96cf":function(t,e,n){var i=function(t){"use strict";var e,n=Object.prototype,i=n.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"===typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(M){l=function(t,e,n){return t[e]=n}}function h(t,e,n,i){var o=e&&e.prototype instanceof v?e:v,a=Object.create(o.prototype),s=new L(i||[]);return r(a,"_invoke",{value:I(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(M){return{type:"throw",arg:M}}}t.wrap=h;var u="suspendedStart",p="suspendedYield",m="executing",g="completed",f={};function v(){}function E(){}function y(){}var b={};l(b,a,(function(){return this}));var x=Object.getPrototypeOf,C=x&&x(x(O([])));C&&C!==n&&i.call(C,a)&&(b=C);var T=y.prototype=v.prototype=Object.create(b);function D(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function n(r,o,a,s){var c=d(t[r],t,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"===typeof h&&i.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(h).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var o;function a(t,i){function r(){return new e((function(e,r){n(t,i,e,r)}))}return o=o?o.then(r,r):r()}r(this,"_invoke",{value:a})}function I(t,e,n){var i=u;return function(r,o){if(i===m)throw new Error("Generator is already running");if(i===g){if("throw"===r)throw o;return _()}n.method=r,n.arg=o;while(1){var a=n.delegate;if(a){var s=k(a,n);if(s){if(s===f)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===u)throw i=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=m;var c=d(t,e,n);if("normal"===c.type){if(i=n.done?g:p,c.arg===f)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=g,n.method="throw",n.arg=c.arg)}}}function k(t,n){var i=n.method,r=t.iterator[i];if(r===e)return n.delegate=null,"throw"===i&&t.iterator["return"]&&(n.method="return",n.arg=e,k(t,n),"throw"===n.method)||"return"!==i&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+i+"' method")),f;var o=d(r,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,f;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,f):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function O(t){if(null!=t){var n=t[a];if(n)return n.call(t);if("function"===typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function n(){while(++r<t.length)if(i.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}function _(){return{value:e,done:!0}}return E.prototype=y,r(T,"constructor",{value:y,configurable:!0}),r(y,"constructor",{value:E,configurable:!0}),E.displayName=l(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"===typeof t&&t.constructor;return!!e&&(e===E||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,l(t,c,"GeneratorFunction")),t.prototype=Object.create(T),t},t.awrap=function(t){return{__await:t}},D(w.prototype),l(w.prototype,s,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,i,r,o){void 0===o&&(o=Promise);var a=new w(h(e,n,i,r),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},D(T),l(T,c,"Generator"),l(T,a,(function(){return this})),l(T,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var i in e)n.push(i);return n.reverse(),function t(){while(n.length){var i=n.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},t.values=O,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0],e=t.completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(i,r){return s.type="throw",s.arg=t,n.next=i,r&&(n.method="next",n.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;S(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,i){return this.delegate={iterator:O(t),resultName:n,nextLoc:i},"next"===this.method&&(this.arg=e),f}},t}(t.exports);try{regeneratorRuntime=i}catch(r){"object"===typeof globalThis?globalThis.regeneratorRuntime=i:Function("r","regeneratorRuntime = r")(i)}},"990b":function(t,e,n){var i=n("9093"),r=n("2621"),o=n("cb7c"),a=n("7726").Reflect;t.exports=a&&a.ownKeys||function(t){var e=i.f(o(t)),n=r.f;return n?e.concat(n(t)):e}},"9b43":function(t,e,n){var i=n("d8e8");t.exports=function(t,e,n){if(i(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,i){return t.call(e,n,i)};case 3:return function(n,i,r){return t.call(e,n,i,r)}}return function(){return t.apply(e,arguments)}}},"9c6c":function(t,e,n){var i=n("2b4c")("unscopables"),r=Array.prototype;void 0==r[i]&&n("32e9")(r,i,{}),t.exports=function(t){r[i][t]=!0}},"9def":function(t,e,n){var i=n("4588"),r=Math.min;t.exports=function(t){return t>0?r(i(t),9007199254740991):0}},"9e1e":function(t,e,n){t.exports=!n("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},a481:function(t,e,n){"use strict";var i=n("cb7c"),r=n("4bf8"),o=n("9def"),a=n("4588"),s=n("0390"),c=n("5f1b"),l=Math.max,h=Math.min,d=Math.floor,u=/\$([$&`']|\d\d?|<[^>]*>)/g,p=/\$([$&`']|\d\d?)/g,m=function(t){return void 0===t?t:String(t)};n("214f")("replace",2,(function(t,e,n,g){return[function(i,r){var o=t(this),a=void 0==i?void 0:i[e];return void 0!==a?a.call(i,o,r):n.call(String(o),i,r)},function(t,e){var r=g(n,t,this,e);if(r.done)return r.value;var d=i(t),u=String(this),p="function"===typeof e;p||(e=String(e));var v=d.global;if(v){var E=d.unicode;d.lastIndex=0}var y=[];while(1){var b=c(d,u);if(null===b)break;if(y.push(b),!v)break;var x=String(b[0]);""===x&&(d.lastIndex=s(u,o(d.lastIndex),E))}for(var C="",T=0,D=0;D<y.length;D++){b=y[D];for(var w=String(b[0]),I=l(h(a(b.index),u.length),0),k=[],A=1;A<b.length;A++)k.push(m(b[A]));var S=b.groups;if(p){var L=[w].concat(k,I,u);void 0!==S&&L.push(S);var O=String(e.apply(void 0,L))}else O=f(w,u,I,k,S,e);I>=T&&(C+=u.slice(T,I)+O,T=I+w.length)}return C+u.slice(T)}];function f(t,e,i,o,a,s){var c=i+t.length,l=o.length,h=p;return void 0!==a&&(a=r(a),h=u),n.call(s,h,(function(n,r){var s;switch(r.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,i);case"'":return e.slice(c);case"<":s=a[r.slice(1,-1)];break;default:var h=+r;if(0===h)return n;if(h>l){var u=d(h/10);return 0===u?n:u<=l?void 0===o[u-1]?r.charAt(1):o[u-1]+r.charAt(1):n}s=o[h-1]}return void 0===s?"":s}))}}))},aa77:function(t,e,n){var i=n("5ca1"),r=n("be13"),o=n("79e5"),a=n("fdef"),s="["+a+"]",c="​",l=RegExp("^"+s+s+"*"),h=RegExp(s+s+"*$"),d=function(t,e,n){var r={},s=o((function(){return!!a[t]()||c[t]()!=c})),l=r[t]=s?e(u):a[t];n&&(r[n]=l),i(i.P+i.F*s,"String",r)},u=d.trim=function(t,e){return t=String(r(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(h,"")),t};t.exports=d},aae3:function(t,e,n){var i=n("d3f4"),r=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},ac6a:function(t,e,n){for(var i=n("cadf"),r=n("0d58"),o=n("2aba"),a=n("7726"),s=n("32e9"),c=n("84f2"),l=n("2b4c"),h=l("iterator"),d=l("toStringTag"),u=c.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},m=r(p),g=0;g<m.length;g++){var f,v=m[g],E=p[v],y=a[v],b=y&&y.prototype;if(b&&(b[h]||s(b,h,u),b[d]||s(b,d,v),c[v]=u,E))for(f in i)b[f]||o(b,f,i[f],!0)}},b0c5:function(t,e,n){"use strict";var i=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:i!==/./.exec},{exec:i})},ba05:function(t,e,n){},be13:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},c366:function(t,e,n){var i=n("6821"),r=n("9def"),o=n("77f1");t.exports=function(t){return function(e,n,a){var s,c=i(e),l=r(c.length),h=o(a,l);if(t&&n!=n){while(l>h)if(s=c[h++],s!=s)return!0}else for(;l>h;h++)if((t||h in c)&&c[h]===n)return t||h||0;return!t&&-1}}},c4d7:function(t,e,n){"use strict";n("d519")},c5f6:function(t,e,n){"use strict";var i=n("7726"),r=n("69a8"),o=n("2d95"),a=n("5dbc"),s=n("6a99"),c=n("79e5"),l=n("9093").f,h=n("11e9").f,d=n("86cc").f,u=n("aa77").trim,p="Number",m=i[p],g=m,f=m.prototype,v=o(n("2aeb")(f))==p,E="trim"in String.prototype,y=function(t){var e=s(t,!1);if("string"==typeof e&&e.length>2){e=E?e.trim():u(e,3);var n,i,r,o=e.charCodeAt(0);if(43===o||45===o){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:i=2,r=49;break;case 79:case 111:i=8,r=55;break;default:return+e}for(var a,c=e.slice(2),l=0,h=c.length;l<h;l++)if(a=c.charCodeAt(l),a<48||a>r)return NaN;return parseInt(c,i)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(v?c((function(){f.valueOf.call(n)})):o(n)!=p)?a(new g(y(e)),n,m):y(e)};for(var b,x=n("9e1e")?l(g):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),C=0;x.length>C;C++)r(g,b=x[C])&&!r(m,b)&&d(m,b,h(g,b));m.prototype=f,f.constructor=m,n("2aba")(i,p,m)}},c69a:function(t,e,n){t.exports=!n("9e1e")&&!n("79e5")((function(){return 7!=Object.defineProperty(n("230e")("div"),"a",{get:function(){return 7}}).a}))},ca5a:function(t,e){var n=0,i=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+i).toString(36))}},cadf:function(t,e,n){"use strict";var i=n("9c6c"),r=n("d53b"),o=n("84f2"),a=n("6821");t.exports=n("01f9")(Array,"Array",(function(t,e){this._t=a(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,r(1)):r(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},cb50:function(t,e,n){},cb7c:function(t,e,n){var i=n("d3f4");t.exports=function(t){if(!i(t))throw TypeError(t+" is not an object!");return t}},cc9a:function(t,e,n){"use strict";n("8bcf")},cd1c:function(t,e,n){var i=n("e853");t.exports=function(t,e){return new(i(t))(e)}},ce10:function(t,e,n){var i=n("69a8"),r=n("6821"),o=n("c366")(!1),a=n("613b")("IE_PROTO");t.exports=function(t,e){var n,s=r(t),c=0,l=[];for(n in s)n!=a&&i(s,n)&&l.push(n);while(e.length>c)i(s,n=e[c++])&&(~o(l,n)||l.push(n));return l}},d2c8:function(t,e,n){var i=n("aae3"),r=n("be13");t.exports=function(t,e,n){if(i(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(r(t))}},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d519:function(t,e,n){},d53b:function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},d6dd:function(t,e,n){},d8e8:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},dc6d:function(t,e,n){},dcc3:function(t,e,n){"use strict";n("117e")},e003:function(t,e,n){"use strict";n("61c8")},e11e:function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},e61c:function(t,e,n){"use strict";n("d6dd")},e853:function(t,e,n){var i=n("d3f4"),r=n("1169"),o=n("2b4c")("species");t.exports=function(t){var e;return r(t)&&(e=t.constructor,"function"!=typeof e||e!==Array&&!r(e.prototype)||(e=void 0),i(e)&&(e=e[o],null===e&&(e=void 0))),void 0===e?Array:e}},e95c:function(t,e,n){},ef69:function(t,e,n){},f1ae:function(t,e,n){"use strict";var i=n("86cc"),r=n("4630");t.exports=function(t,e,n){e in t?i.f(t,e,r(0,n)):t[e]=n}},f559:function(t,e,n){"use strict";var i=n("5ca1"),r=n("9def"),o=n("d2c8"),a="startsWith",s=""[a];i(i.P+i.F*n("5147")(a),"String",{startsWith:function(t){var e=o(this,t,a),n=r(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),i=String(t);return s?s.call(e,i,n):e.slice(n,n+i.length)===i}})},f6fd:function(t,e){(function(t){var e="currentScript",n=t.getElementsByTagName("script");e in t||Object.defineProperty(t,e,{get:function(){try{throw new Error}catch(i){var t,e=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(i.stack)||[!1])[1];for(t in n)if(n[t].src==e||"interactive"==n[t].readyState)return n[t];return null}}})})(document)},f751:function(t,e,n){var i=n("5ca1");i(i.S+i.F,"Object",{assign:n("7333")})},fa5b:function(t,e,n){t.exports=n("5537")("native-function-to-string",Function.toString)},fab2:function(t,e,n){var i=n("7726").document;t.exports=i&&i.documentElement},fb15:function(t,e,n){"use strict";var i;(n.r(e),"undefined"!==typeof window)&&(n("f6fd"),(i=window.document.currentScript)&&(i=i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(n.p=i[1]));n("7f7f"),n("ac6a"),n("3b2b"),n("cadf"),n("8615"),n("6b54");function r(t){return"[object Object]"===Object.prototype.toString.call(t)}function o(t){return"string"==typeof t}function a(t){return(new Date).getTime()-t<864e5}function s(t){return!t||(!(!Array.isArray(t)||0!=t.length)||!(!r(t)||0!=Object.values(t).length))}function c(t){return t&&"function"===typeof t}n("96cf");function l(t,e,n,i,r,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(i,r)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var o=t.apply(e,n);function a(t){l(o,i,r,a,s,"next",t)}function s(t){l(o,i,r,a,s,"throw",t)}a(void 0)}))}}n("456d"),n("6762"),n("2fdb");var d,u,p=[],m={hover:function(t){},focus:function(t){var e=this;t.addEventListener("focus",(function(t){e.changeVisible()})),t.addEventListener("blur",(function(t){e.changeVisible()}))},click:function(t){var e=this;t.addEventListener("click",(function(t){e.$emit("popoverClick"),t.stopPropagation(),O.hide(),e.changeVisible()}))},contextmenu:function(t){var e=this;t.addEventListener("contextmenu",(function(t){t.preventDefault(),e.changeVisible()}))}},g={name:"LemonPopover",props:{trigger:{type:String,default:"click",validator:function(t){return Object.keys(m).includes(t)}}},data:function(){return{popoverStyle:{},visible:!1}},created:function(){document.addEventListener("click",this._documentClickEvent),p.push(this.close)},mounted:function(){m[this.trigger].call(this,this.$slots.default[0].elm)},render:function(){var t=arguments[0];return t("span",{style:"position:relative"},[t("transition",{attrs:{name:"lemon-slide-top"}},[this.visible&&t("div",{class:"lemon-popover",ref:"popover",style:this.popoverStyle,on:{click:function(t){return t.stopPropagation()}}},[t("div",{class:"lemon-popover__content"},[this.$slots.content]),t("div",{class:"lemon-popover__arrow"})])]),this.$slots.default])},destroyed:function(){document.removeEventListener("click",this._documentClickEvent)},computed:{},watch:{visible:function(){var t=h(regeneratorRuntime.mark((function t(e){var n,i;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e){t.next=6;break}return t.next=3,this.$nextTick();case 3:n=this.$slots.default[0].elm,i=this.$refs.popover,this.popoverStyle={top:"-".concat(i.offsetHeight+10,"px"),left:"".concat(n.offsetWidth/2-i.offsetWidth/2,"px")};case 6:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},methods:{_documentClickEvent:function(t){this.$emit("popoverClick"),t.stopPropagation(),this.visible&&this.close()},changeVisible:function(){this.visible?this.close():this.open()},open:function(){this.closeAll(),this.visible=!0},closeAll:function(){p.forEach((function(t){return t()}))},close:function(){this.visible=!1}}},f=g;n("35f3");function v(t,e,n,i,r,o,a,s){var c,l="function"===typeof t?t.options:t;if(e&&(l.render=e,l.staticRenderFns=n,l._compiled=!0),i&&(l.functional=!0),o&&(l._scopeId="data-v-"+o),a?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),r&&r.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},l._ssrRegister=c):r&&(c=s?function(){r.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:r),c)if(l.functional){l._injectStyles=c;var h=l.render;l.render=function(t,e){return c.call(e),h(t,e)}}else{var d=l.beforeCreate;l.beforeCreate=d?[].concat(d,c):[c]}return{exports:t,options:l}}var E,y=v(f,d,u,!1,null,null,null),b=y.exports,x=function(){E&&(E.style.display="none")},C=function(){E&&(E.style.display="block")};document.addEventListener("click",(function(t){x()}));var T,D,w,I,k,A,S,L,O={hide:x,bind:function(t,e,n){t.addEventListener(e.modifiers.click?"click":"contextmenu",(function(t){if(!s(e.value)&&Array.isArray(e.value)){var i;e.modifiers.click&&t.stopPropagation(),t.preventDefault(),b.methods.closeAll();var r=[];e.modifiers.message?i=n.context:e.modifiers.contact&&(i=n.child),E||(E=document.createElement("div"),E.className="lemon-contextmenu",document.body.appendChild(E)),E.innerHTML=e.value.map((function(t){var e;if(e=c(t.visible)?t.visible(i):void 0===t.visible||t.visible,e){r.push(t);var n=t.icon?'<i class="lemon-contextmenu__icon '.concat(t.icon,'"></i>'):"";return'<div style="color:'.concat(t.color,'" title="').concat(t.text,'" class="lemon-contextmenu__item">').concat(n,"<span>").concat(t.text,"</span></div>")}return""})).join("");var o=E.offsetHeight,a=E.offsetWidth,l=window.innerHeight,h=window.innerWidth,d=t.clientY+o>l?t.pageY-o-5:t.pageY,u=t.clientX+a>h?t.pageX-a-5:t.pageX;E.style.top="".concat(d,"px"),E.style.left="".concat(u,"px"),E.childNodes.forEach((function(t,e){var n=r[e],o=n.click;n.render;t.addEventListener("click",(function(t){t.stopPropagation(),c(o)&&o(t,i,x)}))})),C()}}))}},_={name:"LemonTabs",props:{activeIndex:String},data:function(){return{active:this.activeIndex}},mounted:function(){this.active||(this.active=this.$slots["tab-pane"][0].data.attrs.index)},render:function(){var t=this,e=arguments[0],n=[],i=[];return this.$slots["tab-pane"].map((function(r){var o=r.data.attrs,a=o.tab,s=o.index;n.push(e("div",{class:"lemon-tabs-content__pane",directives:[{name:"show",value:t.active==s}]},[r])),i.push(e("div",{class:["lemon-tabs-nav__item",t.active==s&&"lemon-tabs-nav__item--active"],on:{click:function(){return t._handleNavClick(s)}}},[a]))})),e("div",{class:"lemon-tabs"},[e("div",{class:"lemon-tabs-content"},[n]),e("div",{class:"lemon-tabs-nav"},[i])])},methods:{_handleNavClick:function(t){this.active=t}}},M=_,N=(n("69bb"),v(M,T,D,!1,null,null,null)),P=N.exports,R={name:"LemonButton",props:{color:{type:String,default:"default"},disabled:Boolean},render:function(){var t=arguments[0];return t("button",{class:["lemon-button","lemon-button--color-".concat(this.color)],attrs:{disabled:this.disabled,type:"button"},on:{click:this._handleClick}},[this.$slots.default])},methods:{_handleClick:function(t){this.$emit("click",t)}}},j=R,$=(n("cc9a"),v(j,w,I,!1,null,null,null)),H=$.exports,K=(n("c5f6"),{name:"LemonBadge",props:{count:[Number,Boolean],overflowCount:{type:Number,default:99}},render:function(){var t=arguments[0];return t("span",{class:"lemon-badge"},[this.$slots.default,0!==this.count&&void 0!==this.count&&t("span",{class:["lemon-badge__label",this.isDot&&"lemon-badge__label--dot"]},[this.label])])},computed:{isDot:function(){return!0===this.count},label:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count}},methods:{}}),U=K,F=(n("93f9"),v(U,k,A,!1,null,null,null)),V=F.exports,B={name:"LemonAvatar",inject:["IMUI"],props:{src:String,icon:{type:String,default:"lemon-icon-people"},circle:{type:Boolean,default:function(){return!!this.IMUI&&this.IMUI.avatarCricle}},size:{type:Number,default:32}},data:function(){return{imageFinishLoad:!0}},render:function(){var t=this,e=arguments[0];return e("span",{style:this.style,class:["lemon-avatar",{"lemon-avatar--circle":this.circle}],on:{click:function(e){return t.$emit("click",e)}}},[(this.imageFinishLoad||!this.src)&&e("i",{class:this.icon}),e("img",{attrs:{src:this.src},on:{load:this._handleLoad}})])},computed:{style:function(){var t="".concat(this.size,"px");return{width:t,height:t,lineHeight:t,fontSize:"".concat(this.size/2,"px")}}},methods:{_handleLoad:function(){this.imageFinishLoad=!1}}},Y=B,z=(n("e003"),v(Y,S,L,!1,null,null,null)),G=z.exports,W=n("2638"),q=n.n(W);n("8e6e");function X(t){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},X(t)}function Z(t,e){if("object"!=X(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=X(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function J(t){var e=Z(t,"string");return"symbol"==X(e)?e:e+""}function Q(t,e,n){return(e=J(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}n("a481");function tt(t,e,n){return t?t(n):e}function et(t){return t<10?"0".concat(t):t}function nt(t){var e,n=new Date(t),i=new Date,r=function(t){return t.getFullYear()},o=function(t){return"".concat(t.getMonth()+1,"-").concat(t.getDate())},a=r(n),s=r(i);return e=a!==s?"y-m-d h:i":"".concat(a,"-").concat(o(n))==="".concat(s,"-").concat(o(i))?"h:i":"m-d h:i",it(t,e)}function it(t,e){e||(e="y-m-d h:i:s"),t=t?new Date(t):new Date;for(var n=[t.getFullYear().toString(),et((t.getMonth()+1).toString()),et(t.getDate().toString()),et(t.getHours().toString()),et(t.getMinutes().toString()),et(t.getSeconds().toString())],i="ymdhis",r=0;r<n.length;r++)e=e.replace(i.charAt(r),n[r]);return e}function rt(t,e){c(t)?t((function(){e()})):e()}function ot(t){return t.replace(/<(?!img).*?>/gi,"")}function at(t){if(null==t||""==t)return"0 Bytes";var e=["B","K","M","G","T","P","E","Z","Y"],n=0,i=parseFloat(t);n=Math.floor(Math.log(i)/Math.log(1024));var r=i/Math.pow(1024,n);return r=parseFloat(r.toFixed(2)),r+e[n]}function st(){var t=(new Date).getTime();window.performance&&"function"===typeof window.performance.now&&(t+=performance.now());var e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?n:3&n|8).toString(16)}));return e}var ct,lt,ht={name:"LemonContact",components:{},inject:{IMUI:{from:"IMUI",default:function(){return this}}},data:function(){return{}},props:{contact:Object,simple:Boolean,timeFormat:{type:Function,default:function(t){return it(t,a(t)?"h:i":"y/m/d")}}},render:function(){var t=this,e=arguments[0];return e("div",{class:["lemon-contact",{"lemon-contact--name-center":this.simple}],attrs:{title:this.contact.displayName},on:{click:function(e){return t._handleClick(e,t.contact)}}},[tt(this.$scopedSlots.default,this._renderInner(),this.contact)])},created:function(){},mounted:function(){},computed:{},watch:{},methods:{_renderInner:function(){var t=this.$createElement,e=this.contact;return[t("lemon-badge",{attrs:{count:this.simple?0:e.unread},class:"lemon-contact__avatar"},[t("lemon-avatar",{attrs:{size:40,src:e.avatar}})]),t("div",{class:"lemon-contact__inner"},[t("p",{class:"lemon-contact__label"},[t("span",{class:"lemon-contact__name"},[e.displayName]),!this.simple&&t("span",{class:"lemon-contact__time"},[this.timeFormat(e.lastSendTime)])]),!this.simple&&t("p",{class:"lemon-contact__content"},[o(e.lastContent)?t("span",q()([{},{domProps:{innerHTML:e.lastContent}}])):e.lastContent])])]},_handleClick:function(t,e){this.$emit("click",e)}}},dt=ht,ut=(n("8fb6"),v(dt,ct,lt,!1,null,null,null)),pt=ut.exports;n("5df3"),n("1c4c"),n("9204");const mt=window.ChatArea;var gt=mt;n("6fb5");function ft(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function vt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ft(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ft(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Et,yt,bt,xt,Ct,Tt,Dt,wt=JSON.parse(localStorage.getItem("i18n")),It=function(t,e){document.execCommand(t,!1,e)},kt=window.getSelection(),At=[],St={name:"LemonEditor",inject:{IMUI:{from:"IMUI",default:function(){return this}}},components:{},props:{tools:{type:Array,default:function(){return[]}},sendText:{type:String,default:wt?wt.sendText:"发 送"},wrapKey:{type:Function,default:function(t){return 13==t.keyCode&&!0===t.ctrlKey}},sendKey:{type:Function,default:function(t){return 13==t.keyCode&&0==t.ctrlKey&&0==t.shiftKey}}},data:function(){return this.clipboardBlob=null,{clipboardUrl:"",submitDisabled:!0,accept:"",chatArea:null,curEmoji:"",i18n:this.IMUI.i18n}},created:function(){var t=this;this.IMUI.$on("change-contact",(function(){t.closeClipboardImage()})),this.$nextTick((function(){t.chatArea=new gt({elm:t.$refs.textarea,userList:[],userProps:{id:"id",name:"displayName",avatar:"avatar",pinyin:"name_py"},placeholder:"",needCallSpace:!0,wrapKeyFun:function(e){return t.wrapKey(e)},sendKeyFun:function(e){return t.sendKey(e)}}),t.chatArea.revisePCCheckDialogLabel({title:t.i18n.atTitle,searchPlaceholder:t.i18n.searchPlaceholder,searchEmptyLabel:t.i18n.searchEmptyLabel,userTagTitle:t.i18n.userTagTitle,checkAllLabel:t.i18n.checkAllLabel,confirmLabel:t.i18n.confirmLabel,cancelLabel:t.i18n.cancelLabel}),t.chatArea.revisePCPointDialogLabel({title:t.i18n.groupUserTitle,callEveryLabel:t.i18n.callEveryLabel,checkLabel:t.i18n.checkLabel,emptyLabel:t.i18n.emptyLabel}),t.chatArea.richText.addEventListener("drop",(function(e){var n=e.dataTransfer,i=n.files;Array.from(i).forEach((function(e){t.$emit("upload",e)}))})),t.chatArea.addEventListener("defaultAction",(function(t){switch(t){case"PASTE":return"PREVENT"}})),t.chatArea.richText.addEventListener("paste",(function(e){e.preventDefault();var n=e.clipboardData||window.clipboardData,i=n.getData("Text");if(i)/<[^>]*>/g.test(i)?t.chatArea.reverseAnalysis(i,!0):t.chatArea.insertText(i),t.submitDisabled=!1;else{var r=t._getClipboardBlob(n),o=r.blob,a=r.blobUrl;t.clipboardBlob=o,t.clipboardUrl=a}})),t.chatArea.addEventListener("enterSend",(function(){0==t.submitDisabled&&t._handleSend()}))}))},render:function(){var t=this,e=arguments[0],n=[],i=[];return this.proxyTools.forEach((function(r){var o=r.name,a=r.title,s=r.render,c=r.click,l=r.isRight;c=c||new Function;var h,d=["lemon-editor__tool-item",{"lemon-editor__tool-item--right":l}];h="emoji"==o?0==At.length?"":e("lemon-popover",{class:"lemon-editor__emoji",on:{popoverClick:t.closePointPopup}},[e("template",{slot:"content"},[t._renderEmojiTabs()]),e("div",{class:d,attrs:{title:a}},[s()])]):e("div",{class:d,on:{click:c},attrs:{title:a}},[s()]),l?i.push(h):n.push(h)})),e("lemon-resize",{class:"lemon-editor",attrs:{size:200}},[this.clipboardUrl&&e("div",{class:"lemon-editor__clipboard-image"},[e("img",{attrs:{src:this.clipboardUrl}}),e("div",[e("lemon-button",{style:{marginRight:"10px"},on:{click:this.closeClipboardImage},attrs:{color:"grey"}},[this.i18n.cancelLabel]),e("lemon-button",{on:{click:this.sendClipboardImage}},[this.i18n.sendImage])])]),e("input",{style:"display:none",attrs:{type:"file",multiple:"multiple",accept:this.accept},ref:"fileInput",on:{change:this._handleChangeFile}}),e("div",{class:"lemon-editor__tool"},[e("div",{class:"lemon-editor__tool-left"},[n]),e("div",{class:"lemon-editor__tool-right"},[i])]),e("div",{class:"lemon-editor__inner"},[e("div",{class:"lemon-editor__input",ref:"textarea",on:{click:this._handleClick,input:this._handleInput},attrs:{spellcheck:"false"}})]),e("div",{class:"lemon-editor__footer"},[e("div",{class:"lemon-editor__tip"},[tt(this.IMUI.$scopedSlots["editor-footer"],this.i18n.wrapKey)]),e("div",{class:"lemon-editor__submit"},[e("lemon-button",{attrs:{disabled:this.submitDisabled},on:{click:this._handleSend}},[this.sendText])])])])},computed:{proxyTools:function(){var t=this,e=this.$createElement;if(!this.tools)return[];var n=[{name:"emoji",title:this.i18n.emoji,click:null,render:function(t){return e("i",{class:"lemon-icon-emoji"})}},{name:"uploadFile",title:this.i18n.fileUpload,click:function(){return t.selectFile("*")},render:function(t){return e("i",{class:"lemon-icon-folder"})}},{name:"uploadImage",title:this.i18n.imageUpload,click:function(){return t.selectFile("image/*")},render:function(t){return e("i",{class:"lemon-icon-image"})}}],i=[];if(Array.isArray(this.tools)){var r={emoji:0,uploadFile:1,uploadImage:2},o=Object.keys(r);i=this.tools.map((function(t){return o.includes(t.name)?vt(vt({},n[r[t.name]]),t):t}))}else i=n;return i}},methods:{closePointPopup:function(){this.chatArea&&this.chatArea.chatEvent.winClick()},_sendEmoji:function(t){this.curEmoji=t,this.$emit("send",t.src)},closeClipboardImage:function(){this.clipboardUrl="",this.clipboardBlob=null},sendClipboardImage:function(){this.clipboardBlob&&(this.$emit("upload",this.clipboardBlob),this.closeClipboardImage())},saveRangeToLast:function(){Et||(Et=document.createRange()),Et.selectNodeContents(textarea.value),Et.collapse(!1),kt.removeAllRanges(),kt.addRange(Et)},inertContent:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&saveRangeToLast(),this.focusRange(),It("insertHTML",t),this.saveRange()},saveRange:function(){Et=kt.getRangeAt(0)},focusRange:function(){this.$refs.textarea.focus(),Et&&(kt.removeAllRanges(),kt.addRange(Et))},_handleClick:function(){this.saveRange()},_handleInput:function(){this._checkSubmitDisabled()},_renderEmojiTabs:function(){var t=this,e=this.$createElement,n=function(n,i){return 1==i?n.map((function(n){return e("img",{attrs:{src:n.src,title:n.title,loading:"lazy"},class:"lemon-editor__emoji-item diy-emoji",on:{click:function(){return t._sendEmoji(n)}}})})):n.map((function(n){return e("img",{attrs:{src:n.src,title:n.title},class:"lemon-editor__emoji-item",on:{click:function(){return t._handleSelectEmoji(n)}}})}))};if(At[0].label){var i=At.map((function(t,i){return e("div",{slot:"tab-pane",attrs:{index:i,tab:t.label}},[n(t.children,i)])}));return e("lemon-tabs",{style:"width: 412px"},[i])}return e("div",{class:"lemon-tabs-content",style:"width:406px"},[n(At)])},_handleSelectEmoji:function(t){this.chatArea.insertHtml('<img emoji-name="'.concat(t.name,'" src="').concat(t.src,'"></img>')),this._checkSubmitDisabled()},selectFile:function(){var t=h(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.accept=e,t.next=3,this.$nextTick();case 3:this.$refs.fileInput.click();case 4:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),_getClipboardBlob:function(t){for(var e,n,i=0;i<t.items.length;++i)"file"==t.items[i].kind&&-1!==t.items[i].type.indexOf("image/")&&(e=t.items[i].getAsFile(),n=(window.URL||window.webkitURL).createObjectURL(e));return{blob:e,blobUrl:n}},getFormatValue:function(){return this.IMUI.emojiImageToName(this.chatArea.getHtml({needUserId:!0}))},_checkSubmitDisabled:function(){this.submitDisabled=!ot(this.chatArea.getHtml().trim())},_handleSend:function(t){var e=this.getFormatValue();this.$emit("send",e),this.clear(),this._checkSubmitDisabled()},_handleChangeFile:function(t){var e=this,n=this.$refs.fileInput;Array.from(n.files).forEach((function(t){e.$emit("upload",t)})),n.value=""},clear:function(){this.chatArea.clear()},initEmoji:function(t){At=t,this.$forceUpdate()},setValue:function(t){""==t?this.clear():this.chatArea.reverseAnalysis(this.IMUI.emojiNameToImage(t)),this._checkSubmitDisabled()}}},Lt=St,Ot=(n("3dfc"),n("26fe"),v(Lt,yt,bt,!1,null,null,null)),_t=Ot.exports,Mt=JSON.parse(localStorage.getItem("i18n")),Nt={name:"LemonMessages",inject:{IMUI:{from:"IMUI",default:function(){return this}}},components:{},props:{hideName:Boolean,hideTime:Boolean,reverseUserId:[String,Number],timeRange:{type:Number,default:1},timeFormat:{type:Function,default:function(t){return nt(t)}},loadingText:{type:[String,Function]},loadendText:{type:[String,Function],default:Mt?Mt.noneMsg:"暂无更多消息"},messages:{type:Array,default:function(){return[]}}},data:function(){return this._lockScroll=!1,{_loading:!1,_loadend:!1,isBottom:!0}},render:function(){var t=this,e=arguments[0];return e("div",{class:"lemon-messages",ref:"wrap",on:{scroll:this._handleScroll}},[e("div",{class:["lemon-messages__load","lemon-messages__load--".concat(this._loadend?"end":"ing")]},[e("span",{class:"lemon-messages__loadend"},[o(this.loadendText)?this.loadendText:this.loadendText()]),e("span",{class:"lemon-messages__loading"},[this.loadingText?o(this.loadingText)?this.loadingText:this.loadingText():e("i",{class:"lemon-icon-loading lemonani-spin"})])]),this.messages.map((function(n,i){var r=[],o="["+t.IMUI.i18n.noMsgType+"]",a="lemon-message-".concat(n.type),s=t.toCamelCase(a);t.$options.components[s]||(n.type="text",n.content=o,a="lemon-message-text");var c,l=t.messages[i-1];return l&&t.msecRange&&n.sendTime-l.sendTime>t.msecRange&&r.push(e("lemon-message-event",q()([{},{attrs:{message:{id:"__time__",type:"event",content:nt(n.sendTime)}}}]))),c="event"==n.type?{message:n}:{timeFormat:t.timeFormat,message:n,reverse:t.reverseUserId==n.fromUser.id,hideTime:t.hideTime,hideName:t.hideName},r.push(e(a,q()([{ref:"message",refInFor:!0},{attrs:c}]))),r}))])},computed:{msecRange:function(){return 1e3*this.timeRange*60}},watch:{},methods:{loaded:function(){this._loadend=!0,this.$forceUpdate()},toCamelCase:function(t){return t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}))},resetLoadState:function(){var t=this;this._lockScroll=!0,this._loading=!1,this._loadend=!1,setTimeout((function(){t._lockScroll=!1}),200)},_handleScroll:function(){var t=h(regeneratorRuntime.mark((function t(e){var n,i,r,o,a,s=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this._lockScroll){t.next=2;break}return t.abrupt("return");case 2:if(n=e.target,O.hide(),0!=n.scrollTop||0!=this._loading||0!=this._loadend){t.next=10;break}return this._loading=!0,t.next=8,this.$nextTick();case 8:i=n.scrollHeight,this.$emit("reach-top",function(){var t=h(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s.$nextTick();case 2:n.scrollTop=n.scrollHeight-i,s._loading=!1,s._loadend=!!e;case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 10:r=n.scrollTop,o=n.scrollHeight,a=this.$refs.wrap,this.isBottom=r+a.offsetHeight>=o-20,this.$emit("is-bottom",this.isBottom);case 15:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),scrollToBottom:function(){var t=h(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.$nextTick();case 2:e=this.$refs.wrap,e&&(e.scrollTop=e.scrollHeight);case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},created:function(){},mounted:function(){}},Pt=Nt,Rt=(n("20e3"),v(Pt,xt,Ct,!1,null,null,null)),jt=Rt.exports,$t=function(){var t=this,e=t._self._c;return e("div",{ref:"resize",staticClass:"resize"},[e("div",{ref:"resizeHandle",class:"vertical"==t.direction?"resize-v":"resize-h"}),t._t("default")],2)},Ht=[],Kt={name:"LemonResize",props:{direction:{type:String,default:"vertical"},size:{type:Number,default:250},sizeRange:{type:Array,default:function(){return[200,360]}}},data:function(){return{height:200,width:250}},mounted:function(){this.dragControllerDiv(this.$refs.resize,this.$refs.resizeHandle)},methods:{dragControllerDiv:function(t,e){var n=this;"horizontal"===this.direction?(t.style.width=(localStorage.getItem("horizontalSize")||this.size)+"px",e.onmousedown=function(e){var i=t.offsetWidth,r=e.clientX;document.onmousemove=function(o){var a=o.clientX,s=i+(a-r);n.sizeRange[0]<=s&&n.sizeRange[1]>=s&&(t.style.width=s+"px",n.width=s,n.$emit("box-resize",s,e))},document.onmouseup=function(){localStorage.setItem("horizontalSize",n.width),document.onmousemove=null,document.onmouseup=null}}):(t.style.height=(localStorage.getItem("verticalSize")||this.size)+"px",e.onmousedown=function(e){var i=t.offsetHeight,r=e.clientY;document.onmousemove=function(o){var a=o.clientY,s=i-(a-r);n.sizeRange[0]<=s&&n.sizeRange[1]>=s&&(t.style.height=s+"px",n.height=s,n.$emit("box-resize",s,e))},document.onmouseup=function(){localStorage.setItem("verticalSize",n.height),document.onmousemove=null,document.onmouseup=null}})}}},Ut=Kt,Ft=(n("c4d7"),v(Ut,$t,Ht,!1,null,"3dd7fdc8",null)),Vt=Ft.exports,Bt={name:"lemonMessageBasic",inject:{IMUI:{from:"IMUI",default:function(){return this}}},props:{contextmenu:Array,message:{type:Object,default:function(){return{}}},timeFormat:{type:Function,default:function(){return""}},reverse:Boolean,hideName:Boolean,hideTime:Boolean},data:function(){return{}},render:function(){var t=this,e=arguments[0],n=this.message,i=n.fromUser,r=n.status,o=n.sendTime,a=1==this.hideName&&1==this.hideTime;return e("div",{class:["lemon-message","lemon-message--status-".concat(r),{"lemon-message--reverse":this.reverse,"lemon-message--hide-title":a}]},[e("div",{class:"lemon-message__avatar"},[e("lemon-avatar",{attrs:{size:36,shape:"square",src:i.avatar},on:{click:function(e){t._emitClick(e,"avatar")}}})]),e("div",{class:"lemon-message__inner"},[e("div",{class:"lemon-message__title"},[0==this.hideName&&e("span",{on:{click:function(e){t._emitClick(e,"displayName")}}},[i.displayName]),e("span",{class:"lemon-message__tag"},[tt(this.IMUI.$scopedSlots["message-tag"],null,this.message)]),0==this.hideTime&&e("span",{class:"lemon-message__time",on:{click:function(e){t._emitClick(e,"sendTime")}}},[this.timeFormat(o)])]),e("div",{class:"lemon-message__content-flex"},[e("div",{directives:[{name:"lemon-contextmenu",value:this.IMUI.contextmenu,modifiers:{message:!0}}],class:"lemon-message__content",on:{click:function(e){t._emitClick(e,"content")}}},[tt(this.$scopedSlots["content"],null,this.message)]),e("div",{class:"lemon-message__content-after"},[tt(this.IMUI.$scopedSlots["message-after"],null,this.message)]),e("div",{class:"lemon-message__status",on:{click:function(e){t._emitClick(e,"status")}}},[e("i",{class:"lemon-icon-loading lemonani-spin"}),e("i",{class:"lemon-icon-prompt",attrs:{title:"重发消息"},style:{color:"#ff2525",cursor:"pointer"}})])])])])},created:function(){},mounted:function(){},computed:{},watch:{},methods:{_emitClick:function(t,e){this.IMUI.$emit("message-click",t,e,this.message,this.IMUI)}}},Yt=Bt,zt=(n("628d"),v(Yt,Tt,Dt,!1,null,null,null)),Gt=zt.exports;function Wt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function qt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Wt(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Xt,Zt,Jt={name:"lemonMessageText",inheritAttrs:!1,inject:["IMUI"],render:function(){var t=this,e=arguments[0];return e("lemon-message-basic",q()([{class:"lemon-message-text"},{props:qt({},this.$attrs)},{scopedSlots:{content:function(n){var i=t.IMUI.emojiNameToImage(n.content);return e("span",q()([{},{domProps:{innerHTML:i}}]))}}}]))}},Qt=Jt,te=(n("260f"),v(Qt,Xt,Zt,!1,null,null,null)),ee=te.exports;function ne(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function ie(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ne(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var re,oe,ae={name:"lemonMessageImage",inheritAttrs:!1,render:function(){var t=arguments[0];return t("lemon-message-basic",q()([{class:"lemon-message-image"},{props:ie({},this.$attrs)},{scopedSlots:{content:function(e){return t("img",{attrs:{src:e.content}})}}}]))}},se=ae,ce=(n("e61c"),v(se,re,oe,!1,null,null,null)),le=ce.exports;function he(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function de(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?he(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):he(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var ue,pe,me,ge,fe={name:"lemonMessageFile",inheritAttrs:!1,render:function(){var t=arguments[0];return t("lemon-message-basic",q()([{class:"lemon-message-file"},{props:de({},this.$attrs)},{scopedSlots:{content:function(e){return[t("div",{class:"lemon-message-file__inner"},[t("p",{class:"lemon-message-file__name"},[e.fileName]),t("p",{class:"lemon-message-file__byte"},[at(e.fileSize)])]),t("div",{class:"lemon-message-file__sfx"},[t("i",{class:"lemon-icon-attah"})])]}}}]))}},ve=fe,Ee=(n("85ff"),v(ve,ue,pe,!1,null,null,null)),ye=Ee.exports,be={name:"lemonMessageEvent",inheritAttrs:!1,inject:["IMUI"],render:function(){var t=this,e=arguments[0],n=this.$attrs.message.content;return e("div",{class:"lemon-message lemon-message-event"},[e("span",{class:"lemon-message-event__content",on:{click:function(e){return t._emitClick(e,"content")}}},[n])])},methods:{_emitClick:function(t,e){this.IMUI.$emit("message-click",t,e,this.$attrs.message,this.IMUI)}}},xe=be,Ce=(n("dcc3"),v(xe,me,ge,!1,null,null,null)),Te=Ce.exports;function De(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function we(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?De(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):De(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Ie,ke,Ae={name:"lemonMessageEmoji",inheritAttrs:!1,render:function(){var t=arguments[0];return t("lemon-message-basic",q()([{class:"lemon-message-emoji"},{props:we({},this.$attrs)},{scopedSlots:{content:function(e){return t("img",{attrs:{src:e.content}})}}}]))}},Se=Ae,Le=(n("718e"),v(Se,Ie,ke,!1,null,null,null)),Oe=Le.exports;n("20d6");function _e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function Me(t){if(Array.isArray(t))return _e(t)}function Ne(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function Pe(t,e){if(t){if("string"==typeof t)return _e(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_e(t,e):void 0}}function Re(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function je(t){return Me(t)||Ne(t)||Pe(t)||Re()}n("f559"),n("f751"),n("7514"),n("55dd");var $e="messages",He="contacts",Ke=[$e,He],Ue={sendText:"发 送",sendMsg:"发送消息",sendImage:"发送图片",imageUpload:"图片上传",fileUpload:"文件上传",noneMsg:"暂无更多消息",moreMsg:"查看更多",sendAgain:"重新发送",chat:"聊天",contact:"通讯录",image:"图片",file:"文件",emoji:"表情",emojiDiy:"自定义表情",noMsgType:"暂时不支持此消息",eventNotice:"通知",checkAllLabel:"全选",confirmLabel:"确定",cancelLabel:"取消",wrapKey:"使用Ctrl + enter换行",sendKey:"按Enter发送",draft:"草稿",atTitle:"选择要@的人",searchPlaceholder:"搜素人员名称",searchEmptyLabel:"没有匹配到任何结果",userTagTitle:"成员列表",groupUserTitle:"群成员",callEveryLabel:"所有人",checkLabel:"多选",emptyLabel:"暂无数据"},Fe=JSON.parse(localStorage.getItem("i18n"));Fe||(Fe=Ue);var Ve={file:function(t){return"["+Fe.file+"]"},image:function(t){return"["+Fe.image+"]"},emoji:function(t){return"["+Fe.emojiDiy+"]"},text:function(t){return this.emojiNameToImage(ot(t.content))},event:function(t){return"["+Fe.eventNotice+"]"}};function Be(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ye(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,J(i.key),i)}}function ze(t,e,n){return e&&Ye(t.prototype,e),n&&Ye(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}var Ge=function(){function t(){Be(this,t),this.table={}}return ze(t,[{key:"get",value:function(t){return t?this.table[t]:this.table}},{key:"set",value:function(t,e){this.table[t]=e}},{key:"remove",value:function(t){t?delete this.table[t]:this.table={}}},{key:"has",value:function(t){return!!this.table[t]}}])}();function We(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function qe(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?We(Object(n),!0).forEach((function(e){Q(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):We(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Xe,Ze,Je={},Qe={},tn=function(t){return o(t)?t:"".concat(t,"px")},en=function(t){return t.replace("%","")/100},nn=function(){},rn={name:"LemonImui",provide:function(){return{IMUI:this}},props:{width:{type:[String,Number],default:850},height:{type:[String,Number],default:580},theme:{type:String,default:"default"},simple:{type:Boolean,default:!1},loadingText:[String,Function],loadendText:[String,Function],messageTimeFormat:Function,contactTimeFormat:Function,hideDrawer:{type:Boolean,default:!0},hideMenuAvatar:Boolean,hideMenu:Boolean,hideMessageName:Boolean,hideMessageTime:Boolean,sendKey:Function,wrapKey:Function,sendText:String,contextmenu:Array,contactContextmenu:Array,avatarCricle:Boolean,user:{type:Object,default:function(){return{}}},latelyContacts:{type:Function,default:function(t){var e=t.filter((function(t){return!s(t.lastContent)}));return e.sort((function(t,e){return e.lastSendTime-t.lastSendTime})),e}}},data:function(){return this.CacheContactContainer=new Ge,this.CacheMenuContainer=new Ge,this.CacheMessageLoaded=new Ge,this.CacheDraft=new Ge,{drawerVisible:!this.hideDrawer,currentContactId:null,currentContactIdSidebarContact:null,currentMessages:[],activeSidebar:$e,contacts:[],menus:[],i18n:{},editorTools:[{name:"emoji"},{name:"uploadFile"},{name:"uploadImage"}]}},render:function(){return this._renderWrapper([this._renderMenu(),this._renderSidebarMessage(),this._renderSidebarContact(),this._renderContainer(),this._renderDrawer()])},created:function(){this.initMenus(),this.initI18n()},mounted:function(){var t=h(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.$nextTick();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),computed:{currentContact:function(){var t=this;return this.contacts.find((function(e){return e.id==t.currentContactId}))||{}},currentContactSidebarContact:function(){var t=this;return this.contacts.find((function(e){return e.id==t.currentContactIdSidebarContact}))||{}},currentMenu:function(){var t=this;return this.menus.find((function(e){return e.name==t.activeSidebar}))||{}},currentIsDefSidebar:function(){return Ke.includes(this.activeSidebar)},lastMessages:function(){return this.latelyContacts(this.contacts)}},watch:{activeSidebar:function(){}},methods:{_menuIsContacts:function(){return this.activeSidebar==He},_menuIsMessages:function(){return this.activeSidebar==$e},_createMessage:function(t){return qe(qe({},{id:st(),type:"text",status:"going",sendTime:(new Date).getTime(),toContactId:this.currentContactId,fromUser:qe({},this.user)}),t)},appendMessage:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="+1",i=Je[t.toContactId];if("event"!=t.type&&this.user.id!=t.fromUser.id||(n="+0"),void 0===i)this.updateContact({id:t.toContactId,unread:n,lastSendTime:t.sendTime,lastContent:this.lastContentRender(t)});else{var r=i.some((function(e){var n=e.id;return n==t.id}));if(r)return;this._addMessage(t,t.toContactId,1);var o={id:t.toContactId,lastContent:this.lastContentRender(t),lastSendTime:t.sendTime};t.toContactId==this.currentContactId?(1==e&&this.messageViewToBottom(),this.CacheDraft.remove(t.toContactId)):o.unread=n,this.updateContact(o)}},_emitSend:function(t,e,n){var i=this;this.$emit("send",t,(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{status:"succeed"};e(),i.updateMessage(Object.assign(t,n))}),n)},_handleSend:function(t){var e=this,n=this.$refs.editor.chatArea.getCallUserList(),i=this.$refs.editor.curEmoji.file_id?this.$refs.editor.curEmoji.file_id:0,r=i?"emoji":"text";this.$refs.editor.curEmoji="";var o=n.map((function(t){return t.id})),a=this._createMessage({content:t,at:o,file_id:i,type:r});this.appendMessage(a,!0),this._emitSend(a,(function(){e.updateContact({id:a.toContactId,lastContent:e.lastContentRender(a),lastSendTime:a.sendTime}),e.CacheDraft.remove(a.toContactId)}))},_handleUpload:function(t){var e,n=this,i=["image/gif","image/jpeg","image/png"];e=i.includes(t.type)?{type:"image",content:URL.createObjectURL(t)}:{type:"file",fileSize:t.size,fileName:t.name,content:""};var r=this._createMessage(e);this.appendMessage(r,!0),this._emitSend(r,(function(){n.updateContact({id:r.toContactId,lastContent:n.lastContentRender(r),lastSendTime:r.sendTime})}),t)},_emitPullMessages:function(t){var e=this;this._changeContactLock=!0,this.$emit("pull-messages",this.currentContact,(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e._addMessage(n,e.currentContactId,0),e.CacheMessageLoaded.set(e.currentContactId,i),1==i&&e.$refs.messages.loaded(),e.updateCurrentMessages(),e._changeContactLock=!1,t(i)}),this)},callIsBottom:function(t){this.$emit("is-bottom",t)},clearCacheContainer:function(t){this.CacheContactContainer.remove(t),this.CacheMenuContainer.remove(t)},_renderWrapper:function(t){var e=this.$createElement;return e("div",{style:{width:tn(this.width),height:tn(this.height)},ref:"wrapper",class:["lemon-wrapper","lemon-wrapper--theme-".concat(this.theme),{"lemon-wrapper--simple":this.simple},this.drawerVisible&&"lemon-wrapper--drawer-show"]},[t])},_renderMenu:function(){var t=this,e=this.$createElement,n=this._renderMenuItem();return e("div",{class:"lemon-menu",directives:[{name:"show",value:!this.hideMenu}]},[e("lemon-avatar",{directives:[{name:"show",value:!this.hideMenuAvatar}],on:{click:function(e){t.$emit("menu-avatar-click",e)}},class:"lemon-menu__avatar",attrs:{src:this.user.avatar}}),n.top,this.$slots.menu,e("div",{class:"lemon-menu__bottom"},[this.$slots["menu-bottom"],n.bottom])])},_renderMenuAvatar:function(){},_renderMenuItem:function(){var t=this,e=this.$createElement,n=[],i=[];return this.menus.forEach((function(r){var o=r.name,a=r.title,s=r.unread,c=r.render,l=r.click,h=e("div",{class:["lemon-menu__item",{"lemon-menu__item--active":t.activeSidebar==o}],on:{click:function(){rt(l,(function(){o&&t.changeMenu(o)}))}},attrs:{title:a}},[e("lemon-badge",{attrs:{count:s}},[c(r)])]);!0===r.isBottom?i.push(h):n.push(h)})),{top:n,bottom:i}},_renderSidebarMessage:function(){var t=this;return this._renderSidebar([tt(this.$scopedSlots["sidebar-message-top"],null,this),this.lastMessages.map((function(e){return t._renderContact({contact:e,timeFormat:t.contactTimeFormat},(function(){return t.changeContact(e.id)}),t.$scopedSlots["sidebar-message"])}))],$e,tt(this.$scopedSlots["sidebar-message-fixedtop"],null,this))},_renderContact:function(t,e,n){var i=this,r=this.$createElement,o=t.contact,a=o.click,s=o.renderContainer,c=o.id,l=function(){rt(a,(function(){e(),i._customContainerReady(s,i.CacheContactContainer,c)}))};return r("lemon-contact",q()([{class:{"lemon-contact--active":this.activeSidebar==He?this.currentContactIdSidebarContact==t.contact.id:this.currentContactId==t.contact.id},directives:[{name:"lemon-contextmenu",value:this.contactContextmenu,modifiers:{contact:!0}}]},{props:t},{on:{click:l},scopedSlots:{default:n}}]))},_renderSidebarContact:function(){var t,e=this,n=this.$createElement;return this._renderSidebar([tt(this.$scopedSlots["sidebar-contact-top"],null,this),this.contacts.map((function(i){if(i.index){i.index=i.index.replace(/\[[0-9]*\]/,"");var r=[i.index!==t&&n("p",{class:"lemon-sidebar__label"},[i.index]),e._renderContact({contact:i,simple:!0},(function(){e.changeContact(i.id)}),e.$scopedSlots["sidebar-contact"])];return t=i.index,r}}))],He,tt(this.$scopedSlots["sidebar-contact-fixedtop"],null,this))},_renderSidebar:function(t,e,n){var i=this.$createElement;return i("lemon-resize",{attrs:{direction:"horizontal"},class:"lemon-sidebar",on:{"box-resize":this.boxResize,scroll:this._handleSidebarScroll},directives:[{name:"show",value:this.activeSidebar==e}]},[i("div",{class:"lemon-sidebar__fixed-top"},[n]),i("div",{class:"lemon-sidebar__scroll"},[t])])},_renderDrawer:function(){var t=this.$createElement;return this._menuIsMessages()&&this.currentContactId?t("div",{class:"lemon-drawer",ref:"drawer"},[nn(this.currentContact),tt(this.$scopedSlots.drawer,"",this.currentContact)]):""},_isContactContainerCache:function(t){return t.startsWith("contact#")},_renderContainer:function(){var t=this,e=this.$createElement,n=[],i="lemon-container",r=this.activeSidebar==He?this.currentContactSidebarContact:this.currentContact,o=!0;for(var a in this.CacheContactContainer.get()){var c=r.id==a&&this.currentIsDefSidebar;c&&(o=!c),n.push(e("div",{class:i,directives:[{name:"show",value:c}]},[this.CacheContactContainer.get(a)]))}for(var l in this.CacheMenuContainer.get())n.push(e("div",{class:i,directives:[{name:"show",value:this.activeSidebar==l&&!this.currentIsDefSidebar}]},[this.CacheMenuContainer.get(l)]));return n.push(e("div",{class:i,directives:[{name:"show",value:this._menuIsMessages()&&o&&r.id}]},[e("div",{class:"lemon-container__title"},[tt(this.$scopedSlots["message-title"],e("div",{class:"lemon-container__displayname"},[r.displayName]),r)]),e("div",{class:"lemon-vessel"},[e("div",{class:"lemon-vessel__left"},[e("lemon-messages",{ref:"messages",attrs:{"loading-text":this.loadingText,"loadend-text":this.loadendText,"hide-time":this.hideMessageTime,"hide-name":this.hideMessageName,"time-format":this.messageTimeFormat,"reverse-user-id":this.user.id,messages:this.currentMessages},on:{"reach-top":this._emitPullMessages,"is-bottom":this.callIsBottom}}),e("lemon-editor",{ref:"editor",attrs:{tools:this.editorTools,sendText:this.sendText,sendKey:this.sendKey,wrapKey:this.wrapKey},on:{send:this._handleSend,upload:this._handleUpload}})]),e("div",{class:"lemon-vessel__right"},[tt(this.$scopedSlots["message-side"],null,r)])])])),n.push(e("div",{class:i,directives:[{name:"show",value:!r.id&&this.currentIsDefSidebar}]},[this.$slots.cover])),n.push(e("div",{class:i,directives:[{name:"show",value:this._menuIsContacts()&&o&&r.id}]},[tt(this.$scopedSlots["contact-info"],e("div",{class:"lemon-contact-info"},[e("lemon-avatar",{attrs:{src:r.avatar,size:90}}),e("h4",[r.displayName]),e("lemon-button",{on:{click:function(){s(r.lastContent)&&t.updateContact({id:r.id,lastContent:" "}),t.changeContact(r.id,$e)}}},[this.i18n.sendMsg])]),r)])),n},_handleSidebarScroll:function(){O.hide()},_addContact:function(t,e){var n={0:"unshift",1:"push"}[e];this.contacts[n](t)},_addMessage:function(t,e,n){var i,r={0:"unshift",1:"push"}[n];Array.isArray(t)||(t=[t]),Je[e]=Je[e]||[],(i=Je[e])[r].apply(i,je(t))},setLastContentRender:function(t,e){Ve[t]=e},lastContentRender:function(t){return c(Ve[t.type])?Ve[t.type].call(this,t):"["+this.i18n.noMsgType+"]"},emojiNameToImage:function(t){return t.replace(/\[!(\w+)\]/gi,(function(t,e){var n=e;return Qe[n]?'<img emoji-name="'.concat(e,'" src="').concat(Qe[n],'" />'):"[!".concat(e,"]")}))},emojiImageToName:function(t){return t.replace(/<img emoji-name=\"([^\"]*?)\" [^>]*>/gi,"[!$1]")},updateCurrentMessages:function(){Je[this.currentContactId]||(Je[this.currentContactId]=[]),this.currentMessages=Je[this.currentContactId]},messageViewToBottom:function(){this.$refs.messages.scrollToBottom()},setDraft:function(t,e){if(s(t)||s(e))return!1;var n=this.findContact(t),i=n.lastContent;if(s(n))return!1;this.CacheDraft.has(t)&&(i=this.CacheDraft.get(t).lastContent),this.CacheDraft.set(t,{editorValue:e,lastContent:i}),this.updateContact({id:t,lastContent:'<span style="color:red;">['.concat(this.i18n.draft,"]</span><span>").concat(this.lastContentRender({type:"text",content:e}),"</span>")})},clearDraft:function(t){var e=this.CacheDraft.get(t);if(e){var n=this.findContact(t).lastContent;0===n.indexOf('<span style="color:red;">['+this.i18n.draft+"]</span>")&&this.updateContact({id:t,lastContent:e.lastContent}),this.CacheDraft.remove(t)}},changeContact:function(){var t=h(regeneratorRuntime.mark((function t(e,n){var i,r,o,a=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n){t.next=4;break}this.changeMenu(n),t.next=6;break;case 4:if(!(this._changeContactLock||this.activeSidebar==$e&&this.currentContactId==e||this.activeSidebar==He&&this.currentContactIdSidebarContact==e)){t.next=6;break}return t.abrupt("return",!1);case 6:if(this.currentContactId&&(console.log("🚀 ~ changeContact ~ this.currentContactId:",this.currentContactId),i=this.$refs.editor.chatArea.getHtml({needUserId:!0}),r=this.$refs.editor.chatArea.getText(),r?(this.setDraft(this.currentContactId,i),this.setEditorValue()):this.clearDraft(this.currentContactId)),this.activeSidebar==He?this.currentContactIdSidebarContact=e:this.currentContactId=e,this.currentContactId){t.next=10;break}return t.abrupt("return",!1);case 10:if(this.$emit("change-contact",this.currentContact,this),!c(this.currentContact.renderContainer)&&this.activeSidebar!=He){t.next=13;break}return t.abrupt("return");case 13:o=this.CacheDraft.get(e),o&&(this.$refs.editor.chatArea.reverseAnalysis(o.editorValue),this.$refs.editor._checkSubmitDisabled()),this.CacheMessageLoaded.has(e)?this.$refs.messages.loaded():this.$refs.messages.resetLoadState(),Je[e]?setTimeout((function(){a.updateCurrentMessages(),a.messageViewToBottom()}),0):(this.updateCurrentMessages(),this._emitPullMessages((function(t){a.messageViewToBottom()})));case 17:case"end":return t.stop()}}),t,this)})));function e(e,n){return t.apply(this,arguments)}return e}(),removeMessage:function(t){var e=this.findMessage(t);if(!e)return!1;var n=Je[e.toContactId].findIndex((function(e){var n=e.id;return n==t}));return Je[e.toContactId].splice(n,1),!0},updateMessage:function(t){if(!t.id)return!1;var e=this.findMessage(t.id);return!!e&&(e=Object.assign(e,t,{toContactId:e.toContactId}),!0)},forceUpdateMessage:function(t){if(t){var e=this.$refs.messages.$refs.message;if(e){var n=e.find((function(e){return e.$attrs.message.id==t}));n&&n.$forceUpdate()}}else this.$refs.messages.$forceUpdate()},_customContainerReady:function(t,e,n){c(t)&&!e.has(n)&&e.set(n,t.call(this))},changeMenu:function(t){this.$emit("change-menu",t),this.activeSidebar=t},initEmoji:function(t){var e=[];this.$refs.editor.initEmoji(t),t[0].label?t.forEach((function(t){var n;(n=e).push.apply(n,je(t.children))})):e=t,e.forEach((function(t){var e=t.name,n=t.src;return Qe[e]=n}))},initI18n:function(t){var e=Ue;if(t)e=Object.assign(Ue,t);else{var n=JSON.parse(localStorage.getItem("i18n"));n&&(e=n)}localStorage.setItem("i18n",JSON.stringify(e)),this.i18n=e},initEditorTools:function(t){this.editorTools=t},initMenus:function(t){var e=this,n=this.$createElement,i=[{name:$e,title:this.i18n.chat,unread:0,click:null,render:function(t){return n("i",{class:"lemon-icon-message"})},isBottom:!1},{name:He,title:this.i18n.contact,unread:0,click:null,render:function(t){return n("i",{class:"lemon-icon-addressbook"})},isBottom:!1}],r=[];if(Array.isArray(t)){var o={messages:0,contacts:1},a=Object.keys(o);r=t.map((function(t){return a.includes(t.name)?qe(qe(qe({},i[o[t.name]]),t),{renderContainer:null}):(t.renderContainer&&e._customContainerReady(t.renderContainer,e.CacheMenuContainer,t.name),t)}))}else r=i;this.menus=r},initContacts:function(t){this.contacts=t,this.sortContacts()},sortContacts:function(){this.contacts.sort((function(t,e){if(t.index)return t.index.localeCompare(e.index)}))},appendContact:function(t){return s(t.id)||s(t.displayName)?(console.error("id | displayName cant be empty"),!1):(this.hasContact(t.id)||this.contacts.push(Object.assign({id:"",displayName:"",avatar:"",index:"",unread:0,lastSendTime:"",lastContent:""},t)),!0)},removeContact:function(t){var e=this.findContactIndexById(t);return-1!==e&&(this.contacts.splice(e,1),this.CacheDraft.remove(t),this.CacheMessageLoaded.remove(t),!0)},updateContact:function(t){var e=t.id;delete t.id;var n=this.findContactIndexById(e);if(-1!==n){var i=t.unread;o(i)&&(0!==i.indexOf("+")&&0!==i.indexOf("-")||(t.unread=parseInt(i)+parseInt(this.contacts[n].unread))),this.$set(this.contacts,n,qe(qe({},this.contacts[n]),t))}},findContactIndexById:function(t){return this.contacts.findIndex((function(e){return e.id==t}))},hasContact:function(t){return-1!==this.findContactIndexById(t)},findMessage:function(t){for(var e in Je){var n=Je[e].find((function(e){var n=e.id;return n==t}));if(n)return n}},findContact:function(t){return this.getContacts().find((function(e){var n=e.id;return n==t}))},getContacts:function(){return this.contacts},getCurrentContact:function(){return this.currentContact},getCurrentMessages:function(){return this.currentMessages},setEditorValue:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(!o(t))return!1;this.$refs.editor.setValue(this.emojiNameToImage(t))},getEditorValue:function(){return this.$refs.editor.getFormatValue()},clearMessages:function(t){return t?(delete Je[t],this.CacheMessageLoaded.remove(t),this.CacheDraft.remove(t)):(Je={},this.CacheMessageLoaded.remove(),this.CacheDraft.remove()),!0},getMessages:function(t){return(t?Je[t]:Je)||[]},changeDrawer:function(t){this.drawerVisible=!this.drawerVisible,1==this.drawerVisible&&this.openDrawer(t)},openDrawer:function(t){nn=c(t)?t:t.render||new Function;var e=this.$refs.wrapper.clientWidth,n=this.$refs.wrapper.clientHeight,i=t.width||200,r=t.height||n,a=t.offsetX||0,s=t.offsetY||0,l=t.position||"right";o(i)&&(i=e*en(i)),o(r)&&(r=n*en(r)),o(a)&&(a=e*en(a)),o(s)&&(s=n*en(s)),this.$refs.drawer.style.width="".concat(i,"px"),this.$refs.drawer.style.height="".concat(r,"px");var h=0,d=0,u="";"right"==l?h=e:"rightInside"==l?(h=e-i,u="-15px 0 16px -14px rgba(0,0,0,0.08)"):"center"==l&&(h=e/2-i/2,d=n/2-r/2,u="0 0 20px rgba(0,0,0,0.08)"),h+=a,d+=s+-1,this.$refs.drawer.style.top="".concat(d,"px"),this.$refs.drawer.style.left="".concat(h,"px"),this.$refs.drawer.style.boxShadow=u,this.drawerVisible=!0},boxResize:function(t,e){this.$emit("sidebar-resize",t,e)},closeDrawer:function(){this.drawerVisible=!1},setAtUserList:function(t,e){this.$refs.editor.chatArea.updateConfig({userList:t,needCallEvery:e})},setUserTag:function(t){this.$refs.editor.chatArea.setUserTag(t),this.$refs.editor._checkSubmitDisabled()},getChatArea:function(){return this.$refs.editor.chatArea}}},on=rn,an=(n("3d91"),v(on,Xe,Ze,!1,null,null,null)),sn=an.exports,cn=(n("6a2b"),"2.1.0"),ln=[sn,pt,jt,_t,G,V,H,b,P,Vt,Gt,ee,le,ye,Te,Oe],hn=function(t){t.directive("LemonContextmenu",O),ln.forEach((function(e){t.component(e.name,e)}))};"undefined"!==typeof window&&window.Vue&&hn(window.Vue);var dn={version:cn,install:hn};e["default"]=dn},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}})}));